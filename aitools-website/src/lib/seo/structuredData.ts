import { Tool } from '@/lib/api';

const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com';

// 网站基础结构化数据
export function getWebsiteStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "AI工具导航",
    "description": "发现最好的AI工具，提升您的工作效率和创造力",
    "url": baseUrl,
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/tools?search={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "AI工具导航",
      "url": baseUrl
    }
  };
}

// 组织结构化数据
export function getOrganizationStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "AI工具导航",
    "description": "专业的AI工具发现和推荐平台",
    "url": baseUrl,
    "logo": `${baseUrl}/logo.png`,
    "sameAs": [
      // 可以添加社交媒体链接
    ]
  };
}

// 工具详情页结构化数据
export function getToolStructuredData(tool: Tool) {
  return {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": tool.name,
    "description": tool.description,
    "url": tool.website,
    "applicationCategory": "AI工具",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": tool.pricing === 'free' ? "0" : undefined,
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "aggregateRating": tool.likes ? {
      "@type": "AggregateRating",
      "ratingValue": Math.min(5, Math.max(1, (tool.likes / 10) + 3)), // 简单的评分算法
      "reviewCount": tool.likes,
      "bestRating": 5,
      "worstRating": 1
    } : undefined,
    "image": tool.logo || `${baseUrl}/default-tool-image.jpg`,
    "datePublished": tool.publishedAt,
    "publisher": {
      "@type": "Organization",
      "name": "AI工具导航",
      "url": baseUrl
    }
  };
}

// 面包屑导航结构化数据
export function getBreadcrumbStructuredData(items: Array<{name: string, url: string}>) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": `${baseUrl}${item.url}`
    }))
  };
}

// 工具列表页结构化数据
export function getToolListStructuredData(tools: Tool[], category?: string) {
  return {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": category ? `${category} AI工具` : "AI工具列表",
    "description": category ? `发现最好的${category} AI工具` : "发现最好的AI工具",
    "numberOfItems": tools.length,
    "itemListElement": tools.map((tool, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "item": {
        "@type": "SoftwareApplication",
        "name": tool.name,
        "description": tool.description,
        "url": `${baseUrl}/tools/${tool._id}`,
        "image": tool.logo || `${baseUrl}/default-tool-image.jpg`
      }
    }))
  };
}
