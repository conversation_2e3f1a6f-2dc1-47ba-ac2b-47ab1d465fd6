import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import SessionProvider from "@/components/providers/SessionProvider";
import Header from "@/components/layout/Header";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "AI工具导航 - 发现最好的人工智能工具",
  description: "探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美AI工具。",
  keywords: "AI工具,人工智能,AI导航,机器学习工具,深度学习,自动化工具,AI应用",
  authors: [{ name: "AI工具导航团队" }],
  creator: "AI工具导航",
  publisher: "AI工具导航",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com',
    siteName: 'AI工具导航',
    title: 'AI工具导航 - 发现最好的人工智能工具',
    description: '探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美AI工具。',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'AI工具导航 - 发现最好的人工智能工具',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AI工具导航 - 发现最好的人工智能工具',
    description: '探索精选的人工智能工具集合，提升您的工作效率和创造力。',
    images: ['/og-image.jpg'],
  },
  alternates: {
    canonical: process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com',
  },
  verification: {
    google: 'your-google-verification-code', // 需要替换为实际的验证码
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <SessionProvider>
          <Header />
          <main>
            {children}
          </main>
        </SessionProvider>
      </body>
    </html>
  );
}
