import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import User from '@/models/User';
import { authOptions } from '@/lib/auth';
import mongoose from 'mongoose';

// GET /api/tools/[id] - 获取单个工具详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();

    const { id } = await params;

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, message: '无效的工具ID' },
        { status: 400 }
      );
    }

    // 查找工具
    const tool = await Tool.findById(id);

    if (!tool) {
      return NextResponse.json(
        { success: false, message: '工具不存在' },
        { status: 404 }
      );
    }

    // 检查是否需要认证（对于草稿状态的工具）
    if (tool.status === 'draft') {
      const session = await getServerSession(authOptions);
      if (!session?.user?.email) {
        return NextResponse.json(
          { success: false, message: '请先登录' },
          { status: 401 }
        );
      }

      const user = await User.findOne({ email: session.user.email });
      if (!user || tool.submittedBy !== user._id.toString()) {
        return NextResponse.json(
          { success: false, message: '您没有权限访问此工具' },
          { status: 403 }
        );
      }
    }

    // 如果是公开访问已发布的工具，增加浏览量
    if (tool.status === 'published' && tool.isActive) {
      await Tool.findByIdAndUpdate(id, { $inc: { views: 1 } });
    }

    // 根据访问权限返回不同的数据
    let responseData;
    if (tool.status === 'draft') {
      // 草稿状态返回完整信息给所有者
      responseData = tool;
    } else {
      // 公开工具隐藏敏感信息
      responseData = {
        ...tool.toObject(),
        submittedBy: undefined,
        reviewNotes: undefined,
        reviewedBy: undefined
      };
    }

    return NextResponse.json({
      success: true,
      data: responseData
    });

  } catch (error) {
    console.error('Error fetching tool:', error);
    return NextResponse.json(
      { success: false, message: '获取工具详情失败' },
      { status: 500 }
    );
  }
}

// PUT /api/tools/[id] - 更新工具信息
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查用户认证
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, error: '请先登录' },
        { status: 401 }
      );
    }

    await dbConnect();

    const { id } = await params;
    const body = await request.json();

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: '无效的工具ID' },
        { status: 400 }
      );
    }

    // 获取用户信息
    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, error: '用户不存在' },
        { status: 404 }
      );
    }

    // 查找工具
    const tool = await Tool.findById(id);
    if (!tool) {
      return NextResponse.json(
        { success: false, error: '工具不存在' },
        { status: 404 }
      );
    }

    // 检查用户权限（只能编辑自己提交的工具）
    if (tool.submittedBy !== user._id.toString()) {
      return NextResponse.json(
        { success: false, error: '您没有权限编辑此工具' },
        { status: 403 }
      );
    }

    // 检查工具状态和编辑权限
    const allowedStatuses = ['draft', 'pending', 'rejected', 'approved', 'published'];
    if (!allowedStatuses.includes(tool.status)) {
      return NextResponse.json(
        { success: false, error: '该工具当前状态不允许编辑' },
        { status: 400 }
      );
    }

    // 根据工具状态确定允许更新的字段
    let allowedUpdates: string[] = [];

    if (['draft', 'pending', 'rejected'].includes(tool.status)) {
      // 草稿、待审核、被拒绝状态：可以编辑所有基本信息
      allowedUpdates = [
        'name', 'tagline', 'description', 'website', 'logo',
        'category', 'pricing', 'tags'
      ];
    } else if (tool.status === 'approved') {
      // 已通过审核但未发布：可以编辑基础信息，但不能修改URL
      allowedUpdates = [
        'name', 'tagline', 'description', 'logo'
      ];
    } else if (tool.status === 'published') {
      // 已发布：只能编辑基础描述信息
      allowedUpdates = [
        'name', 'tagline', 'description'
      ];
    }

    const updates: any = {};
    for (const field of allowedUpdates) {
      if (body[field] !== undefined) {
        updates[field] = body[field];
      }
    }

    // 如果更新了名称，检查是否重复
    if (updates.name && updates.name !== tool.name) {
      const existingTool = await Tool.findOne({ 
        name: updates.name, 
        _id: { $ne: id } 
      });
      if (existingTool) {
        return NextResponse.json(
          { success: false, error: '该工具名称已存在' },
          { status: 400 }
        );
      }
    }

    // 执行更新
    const updatedTool = await Tool.findByIdAndUpdate(
      id,
      { $set: updates },
      { new: true, runValidators: true }
    ).select('-submittedBy -reviewNotes -reviewedBy');

    return NextResponse.json({
      success: true,
      data: updatedTool,
      message: '工具更新成功'
    });

  } catch (error) {
    console.error('Error updating tool:', error);
    
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map((err: any) => err.message);
      return NextResponse.json(
        { success: false, error: '验证失败', details: validationErrors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: '更新工具失败' },
      { status: 500 }
    );
  }
}

// DELETE /api/tools/[id] - 删除工具
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();

    const { id } = params;

    // 验证ID格式
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: '无效的工具ID' },
        { status: 400 }
      );
    }

    // 查找并删除工具
    const tool = await Tool.findByIdAndDelete(id);
    if (!tool) {
      return NextResponse.json(
        { success: false, error: '工具不存在' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '工具删除成功'
    });

  } catch (error) {
    console.error('Error deleting tool:', error);
    return NextResponse.json(
      { success: false, error: '删除工具失败' },
      { status: 500 }
    );
  }
}
