import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';

// POST /api/tools/publish - 发布到期的已审核工具（定时任务调用）
export async function POST(request: NextRequest) {
  try {
    await dbConnect();

    // 验证请求来源（可以添加API密钥验证）
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.CRON_SECRET || 'default-secret';
    
    if (authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json(
        { success: false, error: '未授权的请求' },
        { status: 401 }
      );
    }

    const now = new Date();
    
    // 查找所有已审核通过且发布日期已到的工具
    const toolsToPublish = await Tool.find({
      status: 'approved',
      selectedLaunchDate: { $lte: now },
      isActive: true
    });

    const publishedTools = [];
    
    for (const tool of toolsToPublish) {
      try {
        // 更新工具状态为已发布
        const updatedTool = await Tool.findByIdAndUpdate(
          tool._id,
          {
            $set: {
              status: 'published',
              publishedAt: now
            }
          },
          { new: true }
        );
        
        if (updatedTool) {
          publishedTools.push({
            id: updatedTool._id,
            name: updatedTool.name,
            selectedLaunchDate: updatedTool.selectedLaunchDate,
            publishedAt: updatedTool.publishedAt
          });
        }
      } catch (error) {
        console.error(`Failed to publish tool ${tool._id}:`, error);
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        publishedCount: publishedTools.length,
        publishedTools
      },
      message: `成功发布 ${publishedTools.length} 个工具`
    });

  } catch (error) {
    console.error('Auto publish error:', error);
    return NextResponse.json(
      { success: false, error: '自动发布失败' },
      { status: 500 }
    );
  }
}

// GET /api/tools/publish - 获取待发布工具列表（管理员查看）
export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    
    const now = new Date();
    
    // 获取已审核但未发布的工具
    const pendingPublish = await Tool.find({
      status: 'approved',
      selectedLaunchDate: { $gte: now },
      isActive: true
    })
    .sort({ selectedLaunchDate: 1 })
    .limit(limit)
    .select('name selectedLaunchDate launchOption paymentStatus submittedBy')
    .lean();

    // 获取已到发布时间但未发布的工具
    const readyToPublish = await Tool.find({
      status: 'approved',
      selectedLaunchDate: { $lte: now },
      isActive: true
    })
    .sort({ selectedLaunchDate: 1 })
    .limit(limit)
    .select('name selectedLaunchDate launchOption paymentStatus submittedBy')
    .lean();

    return NextResponse.json({
      success: true,
      data: {
        pendingPublish: pendingPublish.length,
        readyToPublish: readyToPublish.length,
        pendingTools: pendingPublish,
        readyTools: readyToPublish
      }
    });

  } catch (error) {
    console.error('Get publish status error:', error);
    return NextResponse.json(
      { success: false, error: '获取发布状态失败' },
      { status: 500 }
    );
  }
}
