[{"/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx": "1", "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx": "2", "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx": "3", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts": "4", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts": "5", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts": "6", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts": "7", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts": "8", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts": "9", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts": "10", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts": "11", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts": "12", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts": "13", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts": "14", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts": "15", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts": "16", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts": "17", "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx": "18", "/Users/<USER>/workspace/aitools/aitools-website/src/app/dashboard/page.tsx": "19", "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx": "20", "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx": "21", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/liked/page.tsx": "22", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx": "23", "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/submitted/page.tsx": "24", "/Users/<USER>/workspace/aitools/aitools-website/src/app/settings/page.tsx": "25", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx": "26", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-auth/page.tsx": "27", "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx": "28", "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx": "29", "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx": "30", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx": "31", "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx": "32", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx": "33", "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx": "34", "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx": "35", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx": "36", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx": "37", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx": "38", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx": "39", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx": "40", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx": "41", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx": "42", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx": "43", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts": "44", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts": "45", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts": "46", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts": "47", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts": "48", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts": "49", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts": "50", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts": "51", "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts": "52", "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts": "53", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts": "54", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts": "55", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts": "56", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts": "57", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts": "58", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts": "59", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts": "60", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts": "61", "/Users/<USER>/workspace/aitools/aitools-website/src/app/payment/checkout/page.tsx": "62", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit-launch-date/[toolId]/page.tsx": "63", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/launch-date/[toolId]/page.tsx": "64", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/success/page.tsx": "65", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx": "66", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx": "67", "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx": "68", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts": "69", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts": "70", "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit/[toolId]/page.tsx": "71", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts": "72", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts": "73", "/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts": "74", "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-pricing/page.tsx": "75", "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryPageClient.tsx": "76", "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx": "77", "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/SEOHead.tsx": "78", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx": "79", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/LazyLoad.tsx": "80", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx": "81", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts": "82", "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/page.tsx": "83", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolsPageClient.tsx": "84"}, {"size": 12560, "mtime": 1750914757080, "results": "85", "hashOfConfig": "86"}, {"size": 17223, "mtime": 1750914371854, "results": "87", "hashOfConfig": "86"}, {"size": 14947, "mtime": 1751014762901, "results": "88", "hashOfConfig": "86"}, {"size": 5321, "mtime": 1750906802986, "results": "89", "hashOfConfig": "86"}, {"size": 1926, "mtime": 1751014639961, "results": "90", "hashOfConfig": "86"}, {"size": 2074, "mtime": 1750918572984, "results": "91", "hashOfConfig": "86"}, {"size": 3121, "mtime": 1751014815428, "results": "92", "hashOfConfig": "86"}, {"size": 171, "mtime": 1750921851894, "results": "93", "hashOfConfig": "86"}, {"size": 3714, "mtime": 1750921931408, "results": "94", "hashOfConfig": "86"}, {"size": 4489, "mtime": 1750930430193, "results": "95", "hashOfConfig": "86"}, {"size": 2376, "mtime": 1750906822203, "results": "96", "hashOfConfig": "86"}, {"size": 4403, "mtime": 1750924179468, "results": "97", "hashOfConfig": "86"}, {"size": 5614, "mtime": 1750951694652, "results": "98", "hashOfConfig": "86"}, {"size": 7170, "mtime": 1751016131915, "results": "99", "hashOfConfig": "86"}, {"size": 4740, "mtime": 1751016315107, "results": "100", "hashOfConfig": "86"}, {"size": 5538, "mtime": 1750952541605, "results": "101", "hashOfConfig": "86"}, {"size": 2484, "mtime": 1750938669998, "results": "102", "hashOfConfig": "86"}, {"size": 8354, "mtime": 1751036842188, "results": "103", "hashOfConfig": "86"}, {"size": 14185, "mtime": 1751018576923, "results": "104", "hashOfConfig": "86"}, {"size": 2545, "mtime": 1751032793319, "results": "105", "hashOfConfig": "86"}, {"size": 11769, "mtime": 1751035262016, "results": "106", "hashOfConfig": "86"}, {"size": 7642, "mtime": 1750951120332, "results": "107", "hashOfConfig": "86"}, {"size": 10389, "mtime": 1750945315721, "results": "108", "hashOfConfig": "86"}, {"size": 20955, "mtime": 1751014907413, "results": "109", "hashOfConfig": "86"}, {"size": 21004, "mtime": 1750945519465, "results": "110", "hashOfConfig": "86"}, {"size": 16243, "mtime": 1751018388834, "results": "111", "hashOfConfig": "86"}, {"size": 4543, "mtime": 1750930937103, "results": "112", "hashOfConfig": "86"}, {"size": 5609, "mtime": 1751036162133, "results": "113", "hashOfConfig": "86"}, {"size": 2376, "mtime": 1751036302291, "results": "114", "hashOfConfig": "86"}, {"size": 1425, "mtime": 1750903550616, "results": "115", "hashOfConfig": "86"}, {"size": 845, "mtime": 1750908285683, "results": "116", "hashOfConfig": "86"}, {"size": 3162, "mtime": 1751023074001, "results": "117", "hashOfConfig": "86"}, {"size": 505, "mtime": 1750908273441, "results": "118", "hashOfConfig": "86"}, {"size": 863, "mtime": 1750908296528, "results": "119", "hashOfConfig": "86"}, {"size": 5768, "mtime": 1750942157899, "results": "120", "hashOfConfig": "86"}, {"size": 4494, "mtime": 1751023171715, "results": "121", "hashOfConfig": "86"}, {"size": 9109, "mtime": 1750930558601, "results": "122", "hashOfConfig": "86"}, {"size": 6661, "mtime": 1750945557905, "results": "123", "hashOfConfig": "86"}, {"size": 4438, "mtime": 1750923424688, "results": "124", "hashOfConfig": "86"}, {"size": 867, "mtime": 1750922283437, "results": "125", "hashOfConfig": "86"}, {"size": 362, "mtime": 1750922147686, "results": "126", "hashOfConfig": "86"}, {"size": 8935, "mtime": 1750924218629, "results": "127", "hashOfConfig": "86"}, {"size": 3198, "mtime": 1750951009317, "results": "128", "hashOfConfig": "86"}, {"size": 2449, "mtime": 1750942881883, "results": "129", "hashOfConfig": "86"}, {"size": 7052, "mtime": 1751014618384, "results": "130", "hashOfConfig": "86"}, {"size": 5059, "mtime": 1750930729612, "results": "131", "hashOfConfig": "86"}, {"size": 921, "mtime": 1750903252798, "results": "132", "hashOfConfig": "86"}, {"size": 6818, "mtime": 1750903357994, "results": "133", "hashOfConfig": "86"}, {"size": 1667, "mtime": 1750903308052, "results": "134", "hashOfConfig": "86"}, {"size": 2141, "mtime": 1750921803605, "results": "135", "hashOfConfig": "86"}, {"size": 5025, "mtime": 1751014604563, "results": "136", "hashOfConfig": "86"}, {"size": 3406, "mtime": 1750921782108, "results": "137", "hashOfConfig": "86"}, {"size": 720, "mtime": 1750903327281, "results": "138", "hashOfConfig": "86"}, {"size": 3866, "mtime": 1750984404444, "results": "139", "hashOfConfig": "86"}, {"size": 2238, "mtime": 1750995712168, "results": "140", "hashOfConfig": "86"}, {"size": 4057, "mtime": 1751018497440, "results": "141", "hashOfConfig": "86"}, {"size": 5242, "mtime": 1751013821668, "results": "142", "hashOfConfig": "86"}, {"size": 1022, "mtime": 1750984456438, "results": "143", "hashOfConfig": "86"}, {"size": 11755, "mtime": 1751017957312, "results": "144", "hashOfConfig": "86"}, {"size": 2237, "mtime": 1750949131424, "results": "145", "hashOfConfig": "86"}, {"size": 3202, "mtime": 1750953105864, "results": "146", "hashOfConfig": "86"}, {"size": 8048, "mtime": 1751018710311, "results": "147", "hashOfConfig": "86"}, {"size": 6764, "mtime": 1751005731451, "results": "148", "hashOfConfig": "86"}, {"size": 4621, "mtime": 1751005744888, "results": "149", "hashOfConfig": "86"}, {"size": 12431, "mtime": 1751004268664, "results": "150", "hashOfConfig": "86"}, {"size": 3885, "mtime": 1751018851458, "results": "151", "hashOfConfig": "86"}, {"size": 7687, "mtime": 1751017905894, "results": "152", "hashOfConfig": "86"}, {"size": 3527, "mtime": 1751018284048, "results": "153", "hashOfConfig": "86"}, {"size": 3985, "mtime": 1751017840303, "results": "154", "hashOfConfig": "86"}, {"size": 3989, "mtime": 1750984256539, "results": "155", "hashOfConfig": "86"}, {"size": 23256, "mtime": 1751018327971, "results": "156", "hashOfConfig": "86"}, {"size": 3508, "mtime": 1751014666579, "results": "157", "hashOfConfig": "86"}, {"size": 4413, "mtime": 1751019030952, "results": "158", "hashOfConfig": "86"}, {"size": 1968, "mtime": 1751019647414, "results": "159", "hashOfConfig": "86"}, {"size": 6972, "mtime": 1751018762448, "results": "160", "hashOfConfig": "86"}, {"size": 7014, "mtime": 1751036885085, "results": "161", "hashOfConfig": "86"}, {"size": 5275, "mtime": 1751023043127, "results": "162", "hashOfConfig": "86"}, {"size": 2605, "mtime": 1751019665417, "results": "163", "hashOfConfig": "86"}, {"size": 9215, "mtime": 1751036184440, "results": "164", "hashOfConfig": "86"}, {"size": 5433, "mtime": 1751023279983, "results": "165", "hashOfConfig": "86"}, {"size": 3048, "mtime": 1751023262678, "results": "166", "hashOfConfig": "86"}, {"size": 3275, "mtime": 1751019689085, "results": "167", "hashOfConfig": "86"}, {"size": 8136, "mtime": 1751036592697, "results": "168", "hashOfConfig": "86"}, {"size": 8290, "mtime": 1751036340211, "results": "169", "hashOfConfig": "86"}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1ifueu7", {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx", ["422", "423", "424", "425", "426", "427", "428", "429", "430", "431"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/page.tsx", ["432", "433", "434", "435", "436", "437"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/tools/[id]/page.tsx", ["438", "439", "440", "441", "442"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts", ["443", "444"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts", ["445", "446"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts", ["447"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts", ["448", "449"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts", ["450", "451", "452"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts", ["453"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/[slug]/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/dashboard/page.tsx", ["454", "455"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx", ["456", "457"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/liked/page.tsx", ["458", "459"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx", ["460", "461", "462"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/submitted/page.tsx", ["463"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/settings/page.tsx", ["464", "465", "466", "467", "468", "469"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx", ["470"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-auth/page.tsx", ["471", "472", "473", "474"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/[id]/page.tsx", ["475", "476", "477", "478", "479", "480", "481", "482", "483", "484", "485"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/tools/page.tsx", ["486"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx", ["487", "488", "489"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenu.tsx", ["490", "491"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx", ["492", "493", "494", "495"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts", ["496"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts", ["497", "498", "499", "500"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts", ["501"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts", ["502"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts", ["503"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts", ["504", "505"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts", ["506", "507", "508", "509"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/payment/checkout/page.tsx", ["510", "511", "512", "513", "514"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit-launch-date/[toolId]/page.tsx", ["515", "516"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/launch-date/[toolId]/page.tsx", ["517", "518"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/success/page.tsx", ["519", "520", "521"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx", ["522"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx", ["523"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/edit/[toolId]/page.tsx", ["524"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts", ["525"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/test-pricing/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryPageClient.tsx", ["526"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx", ["527", "528", "529"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/SEOHead.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx", ["530", "531", "532", "533", "534", "535"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/LazyLoad.tsx", ["536"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx", ["537", "538", "539"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/categories/page.tsx", ["540"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolsPageClient.tsx", ["541"], [], {"ruleId": "542", "severity": 2, "message": "543", "line": 11, "column": 3, "nodeType": null, "messageId": "544", "endLine": 11, "endColumn": 8}, {"ruleId": "542", "severity": 2, "message": "545", "line": 16, "column": 3, "nodeType": null, "messageId": "544", "endLine": 16, "endColumn": 8}, {"ruleId": "542", "severity": 2, "message": "546", "line": 17, "column": 3, "nodeType": null, "messageId": "544", "endLine": 17, "endColumn": 11}, {"ruleId": "542", "severity": 2, "message": "547", "line": 20, "column": 3, "nodeType": null, "messageId": "544", "endLine": 20, "endColumn": 7}, {"ruleId": "542", "severity": 2, "message": "548", "line": 25, "column": 7, "nodeType": null, "messageId": "544", "endLine": 25, "endColumn": 21}, {"ruleId": "549", "severity": 1, "message": "550", "line": 48, "column": 6, "nodeType": "551", "endLine": 48, "endColumn": 17, "suggestions": "552"}, {"ruleId": "542", "severity": 2, "message": "553", "line": 62, "column": 14, "nodeType": null, "messageId": "544", "endLine": 62, "endColumn": 17}, {"ruleId": "542", "severity": 2, "message": "554", "line": 69, "column": 9, "nodeType": null, "messageId": "544", "endLine": 69, "endColumn": 19}, {"ruleId": "542", "severity": 2, "message": "555", "line": 78, "column": 9, "nodeType": null, "messageId": "544", "endLine": 78, "endColumn": 24}, {"ruleId": "542", "severity": 2, "message": "556", "line": 91, "column": 9, "nodeType": null, "messageId": "544", "endLine": 91, "endColumn": 27}, {"ruleId": "542", "severity": 2, "message": "557", "line": 17, "column": 3, "nodeType": null, "messageId": "544", "endLine": 17, "endColumn": 17}, {"ruleId": "549", "severity": 1, "message": "558", "line": 61, "column": 6, "nodeType": "551", "endLine": 61, "endColumn": 20, "suggestions": "559"}, {"ruleId": "542", "severity": 2, "message": "553", "line": 78, "column": 14, "nodeType": null, "messageId": "544", "endLine": 78, "endColumn": 17}, {"ruleId": "542", "severity": 2, "message": "553", "line": 111, "column": 14, "nodeType": null, "messageId": "544", "endLine": 111, "endColumn": 17}, {"ruleId": "542", "severity": 2, "message": "553", "line": 137, "column": 14, "nodeType": null, "messageId": "544", "endLine": 137, "endColumn": 17}, {"ruleId": "560", "severity": 1, "message": "561", "line": 304, "column": 27, "nodeType": "562", "endLine": 308, "endColumn": 29}, {"ruleId": "542", "severity": 2, "message": "563", "line": 74, "column": 9, "nodeType": null, "messageId": "544", "endLine": 74, "endColumn": 15}, {"ruleId": "542", "severity": 2, "message": "564", "line": 88, "column": 14, "nodeType": null, "messageId": "544", "endLine": 88, "endColumn": 19}, {"ruleId": "542", "severity": 2, "message": "564", "line": 104, "column": 14, "nodeType": null, "messageId": "544", "endLine": 104, "endColumn": 19}, {"ruleId": "560", "severity": 1, "message": "561", "line": 173, "column": 15, "nodeType": "562", "endLine": 177, "endColumn": 17}, {"ruleId": "560", "severity": 1, "message": "561", "line": 267, "column": 19, "nodeType": "562", "endLine": 272, "endColumn": 21}, {"ruleId": "565", "severity": 2, "message": "566", "line": 19, "column": 18, "nodeType": "567", "messageId": "568", "endLine": 19, "endColumn": 21, "suggestions": "569"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 55, "column": 22, "nodeType": "567", "messageId": "568", "endLine": 55, "endColumn": 25, "suggestions": "570"}, {"ruleId": "542", "severity": 2, "message": "571", "line": 8, "column": 27, "nodeType": null, "messageId": "544", "endLine": 8, "endColumn": 34}, {"ruleId": "565", "severity": 2, "message": "566", "line": 96, "column": 23, "nodeType": "567", "messageId": "568", "endLine": 96, "endColumn": 26, "suggestions": "572"}, {"ruleId": "542", "severity": 2, "message": "571", "line": 6, "column": 27, "nodeType": null, "messageId": "544", "endLine": 6, "endColumn": 34}, {"ruleId": "565", "severity": 2, "message": "566", "line": 174, "column": 20, "nodeType": "567", "messageId": "568", "endLine": 174, "endColumn": 23, "suggestions": "573"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 212, "column": 70, "nodeType": "567", "messageId": "568", "endLine": 212, "endColumn": 73, "suggestions": "574"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 22, "column": 18, "nodeType": "567", "messageId": "568", "endLine": 22, "endColumn": 21, "suggestions": "575"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 59, "column": 22, "nodeType": "567", "messageId": "568", "endLine": 59, "endColumn": 25, "suggestions": "576"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 161, "column": 70, "nodeType": "567", "messageId": "568", "endLine": 161, "endColumn": 73, "suggestions": "577"}, {"ruleId": "542", "severity": 2, "message": "564", "line": 56, "column": 14, "nodeType": null, "messageId": "544", "endLine": 56, "endColumn": 19}, {"ruleId": "542", "severity": 2, "message": "548", "line": 22, "column": 7, "nodeType": null, "messageId": "544", "endLine": 22, "endColumn": 21}, {"ruleId": "542", "severity": 2, "message": "553", "line": 109, "column": 14, "nodeType": null, "messageId": "544", "endLine": 109, "endColumn": 17}, {"ruleId": "578", "severity": 2, "message": "579", "line": 198, "column": 13, "nodeType": "562", "endLine": 201, "endColumn": 14}, {"ruleId": "578", "severity": 2, "message": "579", "line": 259, "column": 15, "nodeType": "562", "endLine": 262, "endColumn": 16}, {"ruleId": "549", "severity": 1, "message": "580", "line": 55, "column": 6, "nodeType": "551", "endLine": 55, "endColumn": 49, "suggestions": "581"}, {"ruleId": "542", "severity": 2, "message": "553", "line": 70, "column": 14, "nodeType": null, "messageId": "544", "endLine": 70, "endColumn": 17}, {"ruleId": "542", "severity": 2, "message": "582", "line": 11, "column": 3, "nodeType": null, "messageId": "544", "endLine": 11, "endColumn": 7}, {"ruleId": "542", "severity": 2, "message": "583", "line": 18, "column": 3, "nodeType": null, "messageId": "544", "endLine": 18, "endColumn": 7}, {"ruleId": "560", "severity": 1, "message": "561", "line": 97, "column": 19, "nodeType": "562", "endLine": 101, "endColumn": 21}, {"ruleId": "542", "severity": 2, "message": "564", "line": 104, "column": 14, "nodeType": null, "messageId": "544", "endLine": 104, "endColumn": 19}, {"ruleId": "542", "severity": 2, "message": "584", "line": 12, "column": 3, "nodeType": null, "messageId": "544", "endLine": 12, "endColumn": 7}, {"ruleId": "542", "severity": 2, "message": "585", "line": 15, "column": 3, "nodeType": null, "messageId": "544", "endLine": 15, "endColumn": 6}, {"ruleId": "542", "severity": 2, "message": "553", "line": 94, "column": 14, "nodeType": null, "messageId": "544", "endLine": 94, "endColumn": 17}, {"ruleId": "542", "severity": 2, "message": "553", "line": 111, "column": 14, "nodeType": null, "messageId": "544", "endLine": 111, "endColumn": 17}, {"ruleId": "542", "severity": 2, "message": "553", "line": 128, "column": 14, "nodeType": null, "messageId": "544", "endLine": 128, "endColumn": 17}, {"ruleId": "560", "severity": 1, "message": "561", "line": 197, "column": 23, "nodeType": "562", "endLine": 201, "endColumn": 25}, {"ruleId": "560", "severity": 1, "message": "561", "line": 300, "column": 21, "nodeType": "562", "endLine": 304, "endColumn": 23}, {"ruleId": "542", "severity": 2, "message": "564", "line": 27, "column": 14, "nodeType": null, "messageId": "544", "endLine": 27, "endColumn": 19}, {"ruleId": "542", "severity": 2, "message": "564", "line": 46, "column": 14, "nodeType": null, "messageId": "544", "endLine": 46, "endColumn": 19}, {"ruleId": "565", "severity": 2, "message": "566", "line": 63, "column": 42, "nodeType": "567", "messageId": "568", "endLine": 63, "endColumn": 45, "suggestions": "586"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 64, "column": 42, "nodeType": "567", "messageId": "568", "endLine": 64, "endColumn": 45, "suggestions": "587"}, {"ruleId": "542", "severity": 2, "message": "564", "line": 70, "column": 12, "nodeType": null, "messageId": "544", "endLine": 70, "endColumn": 17}, {"ruleId": "565", "severity": 2, "message": "566", "line": 92, "column": 26, "nodeType": "567", "messageId": "568", "endLine": 92, "endColumn": 29, "suggestions": "588"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 94, "column": 28, "nodeType": "567", "messageId": "568", "endLine": 94, "endColumn": 31, "suggestions": "589"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 103, "column": 21, "nodeType": "567", "messageId": "568", "endLine": 103, "endColumn": 24, "suggestions": "590"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 104, "column": 29, "nodeType": "567", "messageId": "568", "endLine": 104, "endColumn": 32, "suggestions": "591"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 105, "column": 29, "nodeType": "567", "messageId": "568", "endLine": 105, "endColumn": 32, "suggestions": "592"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 106, "column": 27, "nodeType": "567", "messageId": "568", "endLine": 106, "endColumn": 30, "suggestions": "593"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 111, "column": 28, "nodeType": "567", "messageId": "568", "endLine": 111, "endColumn": 31, "suggestions": "594"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 115, "column": 72, "nodeType": "567", "messageId": "568", "endLine": 115, "endColumn": 75, "suggestions": "595"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 163, "column": 44, "nodeType": "567", "messageId": "568", "endLine": 163, "endColumn": 47, "suggestions": "596"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 165, "column": 53, "nodeType": "567", "messageId": "568", "endLine": 165, "endColumn": 56, "suggestions": "597"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 70, "column": 49, "nodeType": "567", "messageId": "568", "endLine": 70, "endColumn": 52, "suggestions": "598"}, {"ruleId": "542", "severity": 2, "message": "564", "line": 44, "column": 14, "nodeType": null, "messageId": "544", "endLine": 44, "endColumn": 19}, {"ruleId": "542", "severity": 2, "message": "564", "line": 84, "column": 14, "nodeType": null, "messageId": "544", "endLine": 84, "endColumn": 19}, {"ruleId": "542", "severity": 2, "message": "564", "line": 111, "column": 14, "nodeType": null, "messageId": "544", "endLine": 111, "endColumn": 19}, {"ruleId": "560", "severity": 1, "message": "561", "line": 61, "column": 13, "nodeType": "562", "endLine": 65, "endColumn": 15}, {"ruleId": "560", "severity": 1, "message": "561", "line": 93, "column": 21, "nodeType": "562", "endLine": 97, "endColumn": 23}, {"ruleId": "542", "severity": 2, "message": "599", "line": 5, "column": 27, "nodeType": null, "messageId": "544", "endLine": 5, "endColumn": 34}, {"ruleId": "542", "severity": 2, "message": "600", "line": 5, "column": 36, "nodeType": null, "messageId": "544", "endLine": 5, "endColumn": 46}, {"ruleId": "549", "severity": 1, "message": "601", "line": 55, "column": 6, "nodeType": "551", "endLine": 55, "endColumn": 14, "suggestions": "602"}, {"ruleId": "560", "severity": 1, "message": "561", "line": 156, "column": 13, "nodeType": "562", "endLine": 160, "endColumn": 15}, {"ruleId": "565", "severity": 2, "message": "566", "line": 4, "column": 34, "nodeType": "567", "messageId": "568", "endLine": 4, "endColumn": 37, "suggestions": "603"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 65, "column": 60, "nodeType": "567", "messageId": "568", "endLine": 65, "endColumn": 63, "suggestions": "604"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 145, "column": 31, "nodeType": "567", "messageId": "568", "endLine": 145, "endColumn": 34, "suggestions": "605"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 151, "column": 26, "nodeType": "567", "messageId": "568", "endLine": 151, "endColumn": 29, "suggestions": "606"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 152, "column": 26, "nodeType": "567", "messageId": "568", "endLine": 152, "endColumn": 29, "suggestions": "607"}, {"ruleId": "542", "severity": 2, "message": "608", "line": 1, "column": 8, "nodeType": null, "messageId": "544", "endLine": 1, "endColumn": 16}, {"ruleId": "542", "severity": 2, "message": "609", "line": 27, "column": 13, "nodeType": null, "messageId": "544", "endLine": 27, "endColumn": 26}, {"ruleId": "542", "severity": 2, "message": "610", "line": 5, "column": 8, "nodeType": null, "messageId": "544", "endLine": 5, "endColumn": 12}, {"ruleId": "542", "severity": 2, "message": "611", "line": 89, "column": 11, "nodeType": null, "messageId": "544", "endLine": 89, "endColumn": 14}, {"ruleId": "565", "severity": 2, "message": "566", "line": 158, "column": 25, "nodeType": "567", "messageId": "568", "endLine": 158, "endColumn": 28, "suggestions": "612"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 38, "column": 18, "nodeType": "567", "messageId": "568", "endLine": 38, "endColumn": 21, "suggestions": "613"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 51, "column": 22, "nodeType": "567", "messageId": "568", "endLine": 51, "endColumn": 25, "suggestions": "614"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 88, "column": 52, "nodeType": "567", "messageId": "568", "endLine": 88, "endColumn": 55, "suggestions": "615"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 89, "column": 52, "nodeType": "567", "messageId": "568", "endLine": 89, "endColumn": 55, "suggestions": "616"}, {"ruleId": "542", "severity": 2, "message": "617", "line": 19, "column": 17, "nodeType": null, "messageId": "544", "endLine": 19, "endColumn": 24}, {"ruleId": "565", "severity": 2, "message": "566", "line": 20, "column": 38, "nodeType": "567", "messageId": "568", "endLine": 20, "endColumn": 41, "suggestions": "618"}, {"ruleId": "549", "severity": 1, "message": "619", "line": 36, "column": 6, "nodeType": "551", "endLine": 36, "endColumn": 23, "suggestions": "620"}, {"ruleId": "542", "severity": 2, "message": "553", "line": 62, "column": 14, "nodeType": null, "messageId": "544", "endLine": 62, "endColumn": 17}, {"ruleId": "542", "severity": 2, "message": "553", "line": 86, "column": 14, "nodeType": null, "messageId": "544", "endLine": 86, "endColumn": 17}, {"ruleId": "565", "severity": 2, "message": "566", "line": 16, "column": 36, "nodeType": "567", "messageId": "568", "endLine": 16, "endColumn": 39, "suggestions": "621"}, {"ruleId": "549", "severity": 1, "message": "622", "line": 32, "column": 6, "nodeType": "551", "endLine": 32, "endColumn": 22, "suggestions": "623"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 18, "column": 36, "nodeType": "567", "messageId": "568", "endLine": 18, "endColumn": 39, "suggestions": "624"}, {"ruleId": "549", "severity": 1, "message": "622", "line": 32, "column": 6, "nodeType": "551", "endLine": 32, "endColumn": 22, "suggestions": "625"}, {"ruleId": "542", "severity": 2, "message": "617", "line": 12, "column": 17, "nodeType": null, "messageId": "544", "endLine": 12, "endColumn": 24}, {"ruleId": "565", "severity": 2, "message": "566", "line": 13, "column": 36, "nodeType": "567", "messageId": "568", "endLine": 13, "endColumn": 39, "suggestions": "626"}, {"ruleId": "549", "severity": 1, "message": "622", "line": 28, "column": 6, "nodeType": "551", "endLine": 28, "endColumn": 22, "suggestions": "627"}, {"ruleId": "542", "severity": 2, "message": "564", "line": 81, "column": 14, "nodeType": null, "messageId": "544", "endLine": 81, "endColumn": 19}, {"ruleId": "542", "severity": 2, "message": "553", "line": 55, "column": 14, "nodeType": null, "messageId": "544", "endLine": 55, "endColumn": 17}, {"ruleId": "560", "severity": 1, "message": "561", "line": 491, "column": 19, "nodeType": "562", "endLine": 495, "endColumn": 21}, {"ruleId": "542", "severity": 2, "message": "571", "line": 5, "column": 27, "nodeType": null, "messageId": "544", "endLine": 5, "endColumn": 34}, {"ruleId": "542", "severity": 2, "message": "628", "line": 52, "column": 60, "nodeType": null, "messageId": "544", "endLine": 52, "endColumn": 72}, {"ruleId": "565", "severity": 2, "message": "566", "line": 50, "column": 33, "nodeType": "567", "messageId": "568", "endLine": 50, "endColumn": 36, "suggestions": "629"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 63, "column": 33, "nodeType": "567", "messageId": "568", "endLine": 63, "endColumn": 36, "suggestions": "630"}, {"ruleId": "565", "severity": 2, "message": "566", "line": 185, "column": 22, "nodeType": "567", "messageId": "568", "endLine": 185, "endColumn": 25, "suggestions": "631"}, {"ruleId": "542", "severity": 2, "message": "632", "line": 3, "column": 27, "nodeType": null, "messageId": "544", "endLine": 3, "endColumn": 36}, {"ruleId": "542", "severity": 2, "message": "633", "line": 8, "column": 10, "nodeType": null, "messageId": "544", "endLine": 8, "endColumn": 19}, {"ruleId": "542", "severity": 2, "message": "634", "line": 25, "column": 57, "nodeType": null, "messageId": "544", "endLine": 25, "endColumn": 63}, {"ruleId": "542", "severity": 2, "message": "635", "line": 26, "column": 16, "nodeType": null, "messageId": "544", "endLine": 26, "endColumn": 23}, {"ruleId": "560", "severity": 1, "message": "561", "line": 39, "column": 19, "nodeType": "562", "endLine": 43, "endColumn": 21}, {"ruleId": "560", "severity": 1, "message": "561", "line": 173, "column": 27, "nodeType": "562", "endLine": 177, "endColumn": 29}, {"ruleId": "560", "severity": 1, "message": "561", "line": 180, "column": 7, "nodeType": "562", "endLine": 189, "endColumn": 9}, {"ruleId": "542", "severity": 2, "message": "636", "line": 37, "column": 10, "nodeType": null, "messageId": "544", "endLine": 37, "endColumn": 18}, {"ruleId": "637", "severity": 1, "message": "638", "line": 78, "column": 9, "nodeType": "562", "endLine": 82, "endColumn": 11}, {"ruleId": "637", "severity": 1, "message": "638", "line": 92, "column": 7, "nodeType": "562", "endLine": 96, "endColumn": 9}, {"ruleId": "542", "severity": 2, "message": "639", "line": 22, "column": 11, "nodeType": null, "messageId": "544", "endLine": 22, "endColumn": 19}, {"ruleId": "542", "severity": 2, "message": "640", "line": 5, "column": 8, "nodeType": null, "messageId": "544", "endLine": 5, "endColumn": 22}, "@typescript-eslint/no-unused-vars", "'Users' is defined but never used.", "unusedVar", "'Heart' is defined but never used.", "'Calendar' is defined but never used.", "'Star' is defined but never used.", "'categoryLabels' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStats'. Either include it or remove the dependency array.", "ArrayExpression", ["641"], "'err' is defined but never used.", "'formatDate' is assigned a value but never used.", "'getActivityIcon' is assigned a value but never used.", "'getActivityBgColor' is assigned a value but never used.", "'MoreHorizontal' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTools'. Either include it or remove the dependency array.", ["642"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'params' is assigned a value but never used.", "'error' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["643", "644"], ["645", "646"], "'request' is defined but never used.", ["647", "648"], ["649", "650"], ["651", "652"], ["653", "654"], ["655", "656"], ["657", "658"], "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/tools/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "React Hook useEffect has a missing dependency: 'filterTools'. Either include it or remove the dependency array.", ["659"], "'User' is defined but never used.", "'Edit' is defined but never used.", "'Mail' is defined but never used.", "'Eye' is defined but never used.", ["660", "661"], ["662", "663"], ["664", "665"], ["666", "667"], ["668", "669"], ["670", "671"], ["672", "673"], ["674", "675"], ["676", "677"], ["678", "679"], ["680", "681"], ["682", "683"], ["684", "685"], "'FaHeart' is defined but never used.", "'FaRegHeart' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["686"], ["687", "688"], ["689", "690"], ["691", "692"], ["693", "694"], ["695", "696"], "'mongoose' is defined but never used.", "'paymentMethod' is assigned a value but never used.", "'Tool' is defined but never used.", "'now' is assigned a value but never used.", ["697", "698"], ["699", "700"], ["701", "702"], ["703", "704"], ["705", "706"], "'session' is assigned a value but never used.", ["707", "708"], "React Hook useEffect has missing dependencies: 'fetchOrderInfo' and 'router'. Either include them or remove the dependency array.", ["709"], ["710", "711"], "React Hook useEffect has missing dependencies: 'fetchToolInfo' and 'router'. Either include them or remove the dependency array.", ["712"], ["713", "714"], ["715"], ["716", "717"], ["718"], "'categoryInfo' is defined but never used.", ["719", "720"], ["721", "722"], ["723", "724"], "'useEffect' is defined but never used.", "'apiClient' is defined but never used.", "'toolId' is defined but never used.", "'setTool' is assigned a value but never used.", "'hasError' is assigned a value but never used.", "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'Category' is defined but never used.", "'LoadingSpinner' is defined but never used.", {"desc": "725", "fix": "726"}, {"desc": "727", "fix": "728"}, {"messageId": "729", "fix": "730", "desc": "731"}, {"messageId": "732", "fix": "733", "desc": "734"}, {"messageId": "729", "fix": "735", "desc": "731"}, {"messageId": "732", "fix": "736", "desc": "734"}, {"messageId": "729", "fix": "737", "desc": "731"}, {"messageId": "732", "fix": "738", "desc": "734"}, {"messageId": "729", "fix": "739", "desc": "731"}, {"messageId": "732", "fix": "740", "desc": "734"}, {"messageId": "729", "fix": "741", "desc": "731"}, {"messageId": "732", "fix": "742", "desc": "734"}, {"messageId": "729", "fix": "743", "desc": "731"}, {"messageId": "732", "fix": "744", "desc": "734"}, {"messageId": "729", "fix": "745", "desc": "731"}, {"messageId": "732", "fix": "746", "desc": "734"}, {"messageId": "729", "fix": "747", "desc": "731"}, {"messageId": "732", "fix": "748", "desc": "734"}, {"desc": "749", "fix": "750"}, {"messageId": "729", "fix": "751", "desc": "731"}, {"messageId": "732", "fix": "752", "desc": "734"}, {"messageId": "729", "fix": "753", "desc": "731"}, {"messageId": "732", "fix": "754", "desc": "734"}, {"messageId": "729", "fix": "755", "desc": "731"}, {"messageId": "732", "fix": "756", "desc": "734"}, {"messageId": "729", "fix": "757", "desc": "731"}, {"messageId": "732", "fix": "758", "desc": "734"}, {"messageId": "729", "fix": "759", "desc": "731"}, {"messageId": "732", "fix": "760", "desc": "734"}, {"messageId": "729", "fix": "761", "desc": "731"}, {"messageId": "732", "fix": "762", "desc": "734"}, {"messageId": "729", "fix": "763", "desc": "731"}, {"messageId": "732", "fix": "764", "desc": "734"}, {"messageId": "729", "fix": "765", "desc": "731"}, {"messageId": "732", "fix": "766", "desc": "734"}, {"messageId": "729", "fix": "767", "desc": "731"}, {"messageId": "732", "fix": "768", "desc": "734"}, {"messageId": "729", "fix": "769", "desc": "731"}, {"messageId": "732", "fix": "770", "desc": "734"}, {"messageId": "729", "fix": "771", "desc": "731"}, {"messageId": "732", "fix": "772", "desc": "734"}, {"messageId": "729", "fix": "773", "desc": "731"}, {"messageId": "732", "fix": "774", "desc": "734"}, {"messageId": "729", "fix": "775", "desc": "731"}, {"messageId": "732", "fix": "776", "desc": "734"}, {"desc": "777", "fix": "778"}, {"messageId": "729", "fix": "779", "desc": "731"}, {"messageId": "732", "fix": "780", "desc": "734"}, {"messageId": "729", "fix": "781", "desc": "731"}, {"messageId": "732", "fix": "782", "desc": "734"}, {"messageId": "729", "fix": "783", "desc": "731"}, {"messageId": "732", "fix": "784", "desc": "734"}, {"messageId": "729", "fix": "785", "desc": "731"}, {"messageId": "732", "fix": "786", "desc": "734"}, {"messageId": "729", "fix": "787", "desc": "731"}, {"messageId": "732", "fix": "788", "desc": "734"}, {"messageId": "729", "fix": "789", "desc": "731"}, {"messageId": "732", "fix": "790", "desc": "734"}, {"messageId": "729", "fix": "791", "desc": "731"}, {"messageId": "732", "fix": "792", "desc": "734"}, {"messageId": "729", "fix": "793", "desc": "731"}, {"messageId": "732", "fix": "794", "desc": "734"}, {"messageId": "729", "fix": "795", "desc": "731"}, {"messageId": "732", "fix": "796", "desc": "734"}, {"messageId": "729", "fix": "797", "desc": "731"}, {"messageId": "732", "fix": "798", "desc": "734"}, {"messageId": "729", "fix": "799", "desc": "731"}, {"messageId": "732", "fix": "800", "desc": "734"}, {"desc": "801", "fix": "802"}, {"messageId": "729", "fix": "803", "desc": "731"}, {"messageId": "732", "fix": "804", "desc": "734"}, {"desc": "805", "fix": "806"}, {"messageId": "729", "fix": "807", "desc": "731"}, {"messageId": "732", "fix": "808", "desc": "734"}, {"desc": "805", "fix": "809"}, {"messageId": "729", "fix": "810", "desc": "731"}, {"messageId": "732", "fix": "811", "desc": "734"}, {"desc": "805", "fix": "812"}, {"messageId": "729", "fix": "813", "desc": "731"}, {"messageId": "732", "fix": "814", "desc": "734"}, {"messageId": "729", "fix": "815", "desc": "731"}, {"messageId": "732", "fix": "816", "desc": "734"}, {"messageId": "729", "fix": "817", "desc": "731"}, {"messageId": "732", "fix": "818", "desc": "734"}, "Update the dependencies array to be: [fetchStats, timeRange]", {"range": "819", "text": "820"}, "Update the dependencies array to be: [fetchTools, statusFilter]", {"range": "821", "text": "822"}, "suggestUnknown", {"range": "823", "text": "824"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "825", "text": "826"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "827", "text": "824"}, {"range": "828", "text": "826"}, {"range": "829", "text": "824"}, {"range": "830", "text": "826"}, {"range": "831", "text": "824"}, {"range": "832", "text": "826"}, {"range": "833", "text": "824"}, {"range": "834", "text": "826"}, {"range": "835", "text": "824"}, {"range": "836", "text": "826"}, {"range": "837", "text": "824"}, {"range": "838", "text": "826"}, {"range": "839", "text": "824"}, {"range": "840", "text": "826"}, "Update the dependencies array to be: [filterTools, likedTools, searchQuery, selectedCategory]", {"range": "841", "text": "842"}, {"range": "843", "text": "824"}, {"range": "844", "text": "826"}, {"range": "845", "text": "824"}, {"range": "846", "text": "826"}, {"range": "847", "text": "824"}, {"range": "848", "text": "826"}, {"range": "849", "text": "824"}, {"range": "850", "text": "826"}, {"range": "851", "text": "824"}, {"range": "852", "text": "826"}, {"range": "853", "text": "824"}, {"range": "854", "text": "826"}, {"range": "855", "text": "824"}, {"range": "856", "text": "826"}, {"range": "857", "text": "824"}, {"range": "858", "text": "826"}, {"range": "859", "text": "824"}, {"range": "860", "text": "826"}, {"range": "861", "text": "824"}, {"range": "862", "text": "826"}, {"range": "863", "text": "824"}, {"range": "864", "text": "826"}, {"range": "865", "text": "824"}, {"range": "866", "text": "826"}, {"range": "867", "text": "824"}, {"range": "868", "text": "826"}, "Update the dependencies array to be: [fetchComments, toolId]", {"range": "869", "text": "870"}, {"range": "871", "text": "824"}, {"range": "872", "text": "826"}, {"range": "873", "text": "824"}, {"range": "874", "text": "826"}, {"range": "875", "text": "824"}, {"range": "876", "text": "826"}, {"range": "877", "text": "824"}, {"range": "878", "text": "826"}, {"range": "879", "text": "824"}, {"range": "880", "text": "826"}, {"range": "881", "text": "824"}, {"range": "882", "text": "826"}, {"range": "883", "text": "824"}, {"range": "884", "text": "826"}, {"range": "885", "text": "824"}, {"range": "886", "text": "826"}, {"range": "887", "text": "824"}, {"range": "888", "text": "826"}, {"range": "889", "text": "824"}, {"range": "890", "text": "826"}, {"range": "891", "text": "824"}, {"range": "892", "text": "826"}, "Update the dependencies array to be: [status, orderId, router, fetchOrderInfo]", {"range": "893", "text": "894"}, {"range": "895", "text": "824"}, {"range": "896", "text": "826"}, "Update the dependencies array to be: [fetchToolInfo, router, status, toolId]", {"range": "897", "text": "898"}, {"range": "899", "text": "824"}, {"range": "900", "text": "826"}, {"range": "901", "text": "898"}, {"range": "902", "text": "824"}, {"range": "903", "text": "826"}, {"range": "904", "text": "898"}, {"range": "905", "text": "824"}, {"range": "906", "text": "826"}, {"range": "907", "text": "824"}, {"range": "908", "text": "826"}, {"range": "909", "text": "824"}, {"range": "910", "text": "826"}, [1125, 1136], "[fetchStats, timeRange]", [1646, 1660], "[fetchTools, statusFilter]", [672, 675], "unknown", [672, 675], "never", [1551, 1554], [1551, 1554], [2591, 2594], [2591, 2594], [4322, 4325], [4322, 4325], [5313, 5316], [5313, 5316], [802, 805], [802, 805], [1657, 1660], [1657, 1660], [4180, 4183], [4180, 4183], [1532, 1575], "[filterTools, likedTools, searchQuery, selectedCategory]", [1763, 1766], [1763, 1766], [1818, 1821], [1818, 1821], [2631, 2634], [2631, 2634], [2700, 2703], [2700, 2703], [2909, 2912], [2909, 2912], [2958, 2961], [2958, 2961], [3018, 3021], [3018, 3021], [3077, 3080], [3077, 3080], [3240, 3243], [3240, 3243], [3357, 3360], [3357, 3360], [5019, 5022], [5019, 5022], [5100, 5103], [5100, 5103], [1877, 1880], [1877, 1880], [1439, 1447], "[fetchComments, toolId]", [137, 140], [137, 140], [2074, 2077], [2074, 2077], [4406, 4409], [4406, 4409], [4562, 4565], [4562, 4565], [4621, 4624], [4621, 4624], [4206, 4209], [4206, 4209], [1171, 1174], [1171, 1174], [1442, 1445], [1442, 1445], [2429, 2432], [2429, 2432], [2517, 2520], [2517, 2520], [809, 812], [809, 812], [1213, 1230], "[status, orderId, router, fetchOrderInfo]", [617, 620], [617, 620], [910, 926], "[fetchToolInfo, router, status, toolId]", [604, 607], [604, 607], [895, 911], [511, 514], [511, 514], [859, 875], [1561, 1564], [1561, 1564], [2024, 2027], [2024, 2027], [4765, 4768], [4765, 4768]]