(()=>{var e={};e.id=6636,e.ids=[6636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13601:(e,s,t)=>{Promise.resolve().then(t.bind(t,92270))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},62185:(e,s,t)=>{"use strict";t.d(s,{u:()=>l});let r=process.env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:3001/api";class a{constructor(e=r){this.baseURL=e}async request(e,s={}){try{let t=`${this.baseURL}${e}`,r={headers:{"Content-Type":"application/json",...s.headers},...s},a=await fetch(t,r),l=await a.json();if(!a.ok)throw Error(l.error||`HTTP error! status: ${a.status}`);return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/tools${t?`?${t}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,s){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(s)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/user/liked-tools${t?`?${t}`:""}`)}async getAdminTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/admin/tools${t?`?${t}`:""}`)}async approveTool(e,s){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(s)})}async rejectTool(e,s){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(s)})}async getAdminStats(e){let s=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${s}`)}async getCategories(){return this.request("/categories")}}let l=new a},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67760:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},75758:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx","default")},77153:(e,s,t)=>{Promise.resolve().then(t.bind(t,75758))},79551:e=>{"use strict";e.exports=require("url")},92270:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var r=t(60687),a=t(43210),l=t(82136),i=t(16189),o=t(85814),n=t.n(o),d=t(98402),c=t(33823);t(62185);let m=(0,t(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var x=t(96474),h=t(53411),p=t(13861),u=t(67760),g=t(40228);function f(){let{data:e,status:s}=(0,l.useSession)();(0,i.useRouter)();let[t,o]=(0,a.useState)({submittedTools:0,approvedTools:0,totalViews:0,totalLikes:0,likedTools:0}),[f,b]=(0,a.useState)([]),[j,v]=(0,a.useState)(!0);return"loading"===s||j?(0,r.jsx)(d.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(c.A,{size:"lg",className:"py-20"})})}):e?(0,r.jsx)(d.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:e.user?.image?(0,r.jsx)("img",{src:e.user.image,alt:e.user.name||"",className:"w-full h-full object-cover"}):(0,r.jsx)("span",{className:"text-2xl font-medium text-gray-600",children:e.user?.name?.charAt(0)||"U"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e.user?.name}),(0,r.jsx)("p",{className:"text-gray-600",children:e.user?.email}),e.user?.role==="admin"&&(0,r.jsx)("span",{className:"inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded",children:"管理员"}),(0,r.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["加入时间：",new Date().toLocaleDateString("zh-CN")]})]})]}),(0,r.jsxs)(n(),{href:"/settings",className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors",children:[(0,r.jsx)(m,{className:"mr-2 h-4 w-4"}),"编辑资料"]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(x.A,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"提交工具"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:t.submittedTools})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(h.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"已通过"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:t.approvedTools})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(p.A,{className:"h-8 w-8 text-purple-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总浏览量"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:t.totalViews})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(u.A,{className:"h-8 w-8 text-red-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"获得点赞"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:t.totalLikes})]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsx)(n(),{href:"/profile/submitted",className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 text-blue-600 mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"我提交的工具"}),(0,r.jsx)("p",{className:"text-gray-600",children:"管理您提交的AI工具"})]})]})}),(0,r.jsx)(n(),{href:"/profile/liked",className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 text-red-600 mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"我的收藏"}),(0,r.jsx)("p",{className:"text-gray-600",children:"查看您收藏的工具"})]})]})}),(0,r.jsx)(n(),{href:"/submit",className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 text-green-600 mr-4"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"提交新工具"}),(0,r.jsx)("p",{className:"text-gray-600",children:"分享您发现的AI工具"})]})]})})]}),f.length>0&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"最近提交的工具"}),(0,r.jsx)(n(),{href:"/profile/submitted",className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"查看全部"})]}),(0,r.jsx)("div",{className:"space-y-4",children:f.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-xs text-gray-500",children:[(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(g.A,{className:"h-3 w-3 mr-1"}),new Date(e.submittedAt).toLocaleDateString("zh-CN")]}),(0,r.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${"approved"===e.status?"bg-green-100 text-green-800":"pending"===e.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"}`,children:"approved"===e.status?"已通过":"pending"===e.status?"审核中":"已拒绝"})]})]}),"approved"===e.status&&(0,r.jsx)(n(),{href:`/tools/${e._id}`,className:"ml-4 text-blue-600 hover:text-blue-700",children:(0,r.jsx)(p.A,{className:"h-5 w-5"})})]},e._id))})]})]})}):null}},99158:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=t(65239),a=t(48088),l=t(88170),i=t.n(l),o=t(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);t.d(s,n);let d={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,75758)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/profile/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,865,1658,6707,7763],()=>t(99158));module.exports=r})();