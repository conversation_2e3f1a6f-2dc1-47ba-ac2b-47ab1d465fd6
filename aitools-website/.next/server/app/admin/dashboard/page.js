(()=>{var e={};e.id=5957,e.ids=[5957],e.modules={1568:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var r=t(60687),a=t(43210),l=t(98402),i=t(33823),n=t(11011),d=t(62185),c=t(48730),o=t(5336),x=t(35071),m=t(62688);let h=(0,m.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);var p=t(53411);let u=(0,m.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var g=t(93613),j=t(13861);function y(){let[e,s]=(0,a.useState)("7d"),[t,m]=(0,a.useState)(null),[y,b]=(0,a.useState)(!0),[f,v]=(0,a.useState)(""),N=async()=>{try{b(!0),v("");let s=await d.u.getAdminStats(e);s.success&&s.data?m(s.data):v(s.error||"获取统计数据失败")}catch(e){v("网络错误，请重试")}finally{b(!1)}};return y?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(i.A,{size:"lg",className:"py-20"})})}):(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[f&&(0,r.jsx)(n.A,{message:f,onClose:()=>v(""),className:"mb-6"}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 flex items-center",children:[(0,r.jsx)(p.A,{className:"mr-3 h-8 w-8 text-blue-600"}),"管理员统计面板"]}),(0,r.jsx)("p",{className:"mt-2 text-lg text-gray-600",children:"查看网站运营数据和审核统计"})]}),(0,r.jsx)("div",{className:"flex items-center space-x-3",children:(0,r.jsxs)("select",{value:e,onChange:e=>s(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"1d",children:"今天"}),(0,r.jsx)("option",{value:"7d",children:"最近7天"}),(0,r.jsx)("option",{value:"30d",children:"最近30天"}),(0,r.jsx)("option",{value:"90d",children:"最近90天"})]})})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"总工具数"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:t?.totalTools||0})]}),(0,r.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,r.jsx)(p.A,{className:"w-6 h-6 text-blue-600"})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,r.jsx)(u,{className:"w-4 h-4 text-green-500 mr-1"}),(0,r.jsx)("span",{className:"text-green-600",children:"+12%"}),(0,r.jsx)("span",{className:"text-gray-500 ml-1",children:"vs 上周"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"待审核"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:t?.pendingReview||0})]}),(0,r.jsx)("div",{className:"p-3 bg-yellow-100 rounded-lg",children:(0,r.jsx)(c.A,{className:"w-6 h-6 text-yellow-600"})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,r.jsx)(g.A,{className:"w-4 h-4 text-yellow-500 mr-1"}),(0,r.jsx)("span",{className:"text-yellow-600",children:"需要关注"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"今日批准"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-600",children:(t?.approvedToday||0).toLocaleString()})]}),(0,r.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,r.jsx)(o.A,{className:"w-6 h-6 text-green-600"})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,r.jsx)(u,{className:"w-4 h-4 text-green-500 mr-1"}),(0,r.jsx)("span",{className:"text-green-600",children:"+8%"}),(0,r.jsx)("span",{className:"text-gray-500 ml-1",children:"vs 上周"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"今日拒绝"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-red-600",children:(t?.rejectedToday||0).toLocaleString()})]}),(0,r.jsx)("div",{className:"p-3 bg-red-100 rounded-lg",children:(0,r.jsx)(x.A,{className:"w-6 h-6 text-red-600"})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,r.jsx)(u,{className:"w-4 h-4 text-green-500 mr-1"}),(0,r.jsx)("span",{className:"text-green-600",children:"+15%"}),(0,r.jsx)("span",{className:"text-gray-500 ml-1",children:"vs 上周"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"审核概览"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(p.A,{className:"w-5 h-5 text-blue-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"总工具数"})]}),(0,r.jsx)("span",{className:"text-lg font-bold text-blue-600",children:t?.totalTools||0})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-yellow-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(c.A,{className:"w-5 h-5 text-yellow-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"待审核"})]}),(0,r.jsx)("span",{className:"text-lg font-bold text-yellow-600",children:t?.pendingReview||0})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-green-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(o.A,{className:"w-5 h-5 text-green-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"今日批准"})]}),(0,r.jsx)("span",{className:"text-lg font-bold text-green-600",children:t?.approvedToday||0})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-red-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(x.A,{className:"w-5 h-5 text-red-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"今日拒绝"})]}),(0,r.jsx)("span",{className:"text-lg font-bold text-red-600",children:t?.rejectedToday||0})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"快速操作"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("button",{onClick:()=>window.location.href="/admin",className:"w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(j.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"查看待审核工具"})]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center space-x-2 p-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,r.jsx)(h,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"查看审核历史"})]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center space-x-2 p-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,r.jsx)(p.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"导出统计报告"})]}),(0,r.jsxs)("button",{onClick:()=>N(),className:"w-full flex items-center justify-center space-x-2 p-3 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors",children:[(0,r.jsx)(u,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"刷新数据"})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"系统信息"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:t?"在线":"离线"}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"系统状态"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:e}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"统计周期"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:new Date().toLocaleDateString("zh-CN")}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:"最后更新"})]})]})]})]})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5973:(e,s,t)=>{"use strict";function r(){return null}t.d(s,{default:()=>r}),t(43210)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11011:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var r=t(60687),a=t(93613),l=t(11860);function i({message:e,onClose:s,className:t=""}){return(0,r.jsx)("div",{className:`bg-red-50 border border-red-200 rounded-lg p-4 ${t}`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:e})}),s&&(0,r.jsx)("button",{onClick:s,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(l.A,{className:"w-4 h-4"})})]})})}},11860:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},17714:(e,s,t)=>{Promise.resolve().then(t.bind(t,1568))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33823:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(60687);function a({size:e="md",className:s=""}){return(0,r.jsx)("div",{className:`flex justify-center items-center ${s}`,children:(0,r.jsx)("div",{className:`${{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[e]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`})})}},33873:e=>{"use strict";e.exports=require("path")},35071:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},53411:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},54666:(e,s,t)=>{Promise.resolve().then(t.bind(t,71031))},62185:(e,s,t)=>{"use strict";t.d(s,{u:()=>l});let r=process.env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:3001/api";class a{constructor(e=r){this.baseURL=e}async request(e,s={}){try{let t=`${this.baseURL}${e}`,r={headers:{"Content-Type":"application/json",...s.headers},...s},a=await fetch(t,r),l=await a.json();if(!a.ok)throw Error(l.error||`HTTP error! status: ${a.status}`);return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/tools${t?`?${t}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,s){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(s)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/user/liked-tools${t?`?${t}`:""}`)}async getAdminTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/admin/tools${t?`?${t}`:""}`)}async approveTool(e,s){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(s)})}async rejectTool(e,s){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(s)})}async getAdminStats(e){let s=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${s}`)}async getCategories(){return this.request("/categories")}}let l=new a},62688:(e,s,t)=>{"use strict";t.d(s,{A:()=>x});var r=t(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),i=e=>{let s=l(e);return s.charAt(0).toUpperCase()+s.slice(1)},n=(...e)=>e.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim(),d=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)(({color:e="currentColor",size:s=24,strokeWidth:t=2,absoluteStrokeWidth:a,className:l="",children:i,iconNode:o,...x},m)=>(0,r.createElement)("svg",{ref:m,...c,width:s,height:s,stroke:e,strokeWidth:a?24*Number(t)/Number(s):t,className:n("lucide",l),...!i&&!d(x)&&{"aria-hidden":"true"},...x},[...o.map(([e,s])=>(0,r.createElement)(e,s)),...Array.isArray(i)?i:[i]])),x=(e,s)=>{let t=(0,r.forwardRef)(({className:t,...l},d)=>(0,r.createElement)(o,{ref:d,iconNode:s,className:n(`lucide-${a(i(e))}`,`lucide-${e}`,t),...l}));return t.displayName=i(e),t}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71031:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},93613:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94722:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=t(65239),a=t(48088),l=t(88170),i=t.n(l),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c={children:["",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,71031)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/Users/<USER>/workspace/aitools/aitools-website/src/app/admin/dashboard/page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/dashboard/page",pathname:"/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},98402:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var r=t(60687);t(43210);var a=t(85814),l=t.n(a),i=t(5973);let n=({children:e})=>(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(i.default,{}),(0,r.jsx)("main",{className:"flex-1",children:e}),(0,r.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"AI Tools"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"发现最新最好的 AI 工具，提升您的工作效率和创造力。"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"快速链接"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:"工具目录"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:"分类浏览"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l(),{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:"提交工具"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:"支持"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"帮助中心"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"联系我们"})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:"隐私政策"})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,r.jsx)("p",{className:"text-center text-gray-600",children:"\xa9 2024 AI Tools Directory. All rights reserved."})})]})})]})}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,865,1658,6707],()=>t(94722));module.exports=r})();