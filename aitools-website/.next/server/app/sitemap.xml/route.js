(()=>{var e={};e.id=5475,e.ids=[5475],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),n=r.n(i);let a=new i.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:["text-generation","image-generation","video-generation","audio-generation","code-generation","data-analysis","productivity","design","marketing","education","research","other"]},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},publishedAt:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","published","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({status:1,isActive:1}),a.index({category:1,status:1}),a.index({tags:1,status:1}),a.index({submittedBy:1}),a.index({publishedAt:-1}),a.index({views:-1}),a.index({likes:-1}),a.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let o=n().models.Tool||n().model("Tool",a)},34490:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>m});var i={};r.r(i),r.d(i,{GET:()=>p});var n=r(96559),a=r(48088),o=r(37719),s=r(32190),l=r(75745),d=r(30762);async function p(e){try{await (0,l.A)();let e=await d.A.find({status:"published"}).select("_id name publishedAt updatedAt").lean(),t=process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",r=`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
  
  <!-- 主页 -->
  <url>
    <loc>${t}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  
  <!-- 工具目录页 -->
  <url>
    <loc>${t}/tools</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
  
  <!-- 分类页 -->
  <url>
    <loc>${t}/categories</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>
  
  <!-- 工具详情页 -->
  ${e.map(e=>`
  <url>
    <loc>${t}/tools/${e._id}</loc>
    <lastmod>${e.updatedAt?new Date(e.updatedAt).toISOString():new Date(e.publishedAt).toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>`).join("")}
  
</urlset>`;return new s.NextResponse(r,{status:200,headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=3600, s-maxage=3600"}})}catch(e){return console.error("Error generating sitemap:",e),new s.NextResponse("Internal Server Error",{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"route",bundlePath:"app/sitemap.xml/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:u,workUnitAsyncStorage:m,serverHooks:g}=c;function y(){return(0,o.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:m})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var i=r(56037),n=r.n(i);let a=process.env.MONGODB_URI;if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let s=async function(){if(o.conn)return o.conn;o.promise||(o.promise=n().connect(a,{bufferCommands:!1}).then(e=>e));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},78335:()=>{},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,580],()=>r(34490));module.exports=i})();