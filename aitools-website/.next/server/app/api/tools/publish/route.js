(()=>{var e={};e.id=6923,e.ids=[6923],e.modules={3080:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var i={};r.r(i),r.d(i,{GET:()=>c,POST:()=>l});var n=r(96559),s=r(48088),a=r(37719),o=r(32190),u=r(75745),d=r(30762);async function l(e){try{await (0,u.A)();let t=e.headers.get("authorization"),r=process.env.CRON_SECRET||"default-secret";if(t!==`Bearer ${r}`)return o.NextResponse.json({success:!1,error:"未授权的请求"},{status:401});let i=new Date,n=await d.A.find({status:"approved",selectedLaunchDate:{$lte:i},isActive:!0}),s=[];for(let e of n)try{let t=await d.A.findByIdAndUpdate(e._id,{$set:{status:"published",publishedAt:i}},{new:!0});t&&s.push({id:t._id,name:t.name,selectedLaunchDate:t.selectedLaunchDate,publishedAt:t.publishedAt})}catch(t){console.error(`Failed to publish tool ${e._id}:`,t)}return o.NextResponse.json({success:!0,data:{publishedCount:s.length,publishedTools:s},message:`成功发布 ${s.length} 个工具`})}catch(e){return console.error("Auto publish error:",e),o.NextResponse.json({success:!1,error:"自动发布失败"},{status:500})}}async function c(e){try{await (0,u.A)();let{searchParams:t}=new URL(e.url),r=parseInt(t.get("limit")||"50"),i=new Date,n=await d.A.find({status:"approved",selectedLaunchDate:{$gte:i},isActive:!0}).sort({selectedLaunchDate:1}).limit(r).select("name selectedLaunchDate launchOption paymentStatus submittedBy").lean(),s=await d.A.find({status:"approved",selectedLaunchDate:{$lte:i},isActive:!0}).sort({selectedLaunchDate:1}).limit(r).select("name selectedLaunchDate launchOption paymentStatus submittedBy").lean();return o.NextResponse.json({success:!0,data:{pendingPublish:n.length,readyToPublish:s.length,pendingTools:n,readyTools:s}})}catch(e){return console.error("Get publish status error:",e),o.NextResponse.json({success:!1,error:"获取发布状态失败"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/tools/publish/route",pathname:"/api/tools/publish",filename:"route",bundlePath:"app/api/tools/publish/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:h}=p;function y(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var i=r(56037),n=r.n(i);let s=new i.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:["text-generation","image-generation","video-generation","audio-generation","code-generation","data-analysis","productivity","design","marketing","education","research","other"]},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},publishedAt:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","published","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});s.index({status:1,isActive:1}),s.index({category:1,status:1}),s.index({tags:1,status:1}),s.index({submittedBy:1}),s.index({publishedAt:-1}),s.index({views:-1}),s.index({likes:-1}),s.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let a=n().models.Tool||n().model("Tool",s)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),n=r.n(i);let s=process.env.MONGODB_URI;if(!s)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let o=async function(){if(a.conn)return a.conn;a.promise||(a.promise=n().connect(s,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,580],()=>r(3080));module.exports=i})();