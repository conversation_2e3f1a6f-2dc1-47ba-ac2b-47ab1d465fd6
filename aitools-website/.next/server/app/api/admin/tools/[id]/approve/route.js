(()=>{var e={};e.id=3229,e.ids=[3229],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var i=r(56037),n=r.n(i);let s=new i.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:["text-generation","image-generation","video-generation","audio-generation","code-generation","data-analysis","productivity","design","marketing","education","research","other"]},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},publishedAt:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","published","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});s.index({status:1,isActive:1}),s.index({category:1,status:1}),s.index({tags:1,status:1}),s.index({submittedBy:1}),s.index({publishedAt:-1}),s.index({views:-1}),s.index({likes:-1}),s.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let a=n().models.Tool||n().model("Tool",s)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),n=r.n(i);let s=process.env.MONGODB_URI;if(!s)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let o=async function(){if(a.conn)return a.conn;a.promise||(a.promise=n().connect(s,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},78335:()=>{},83787:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>m,serverHooks:()=>x,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>y});var i={};r.r(i),r.d(i,{POST:()=>l});var n=r(96559),s=r(48088),a=r(37719),o=r(32190),d=r(75745),p=r(30762),u=r(56037),c=r.n(u);async function l(e,{params:t}){try{await (0,d.A)();let{id:r}=await t,i=await e.json();if(!c().Types.ObjectId.isValid(r))return o.NextResponse.json({success:!1,error:"无效的工具ID"},{status:400});let n=await p.A.findById(r);if(!n)return o.NextResponse.json({success:!1,error:"工具不存在"},{status:404});if("pending"!==n.status)return o.NextResponse.json({success:!1,error:"只能批准待审核状态的工具"},{status:400});let s=await p.A.findByIdAndUpdate(r,{$set:{status:"approved",reviewedAt:new Date,reviewedBy:i.reviewedBy||"admin",reviewNotes:i.reviewNotes||"",isActive:!0},$unset:{rejectedAt:1,rejectReason:1}},{new:!0,runValidators:!0});return o.NextResponse.json({success:!0,data:s,message:"工具已成功批准"})}catch(e){return console.error("Error approving tool:",e),o.NextResponse.json({success:!1,error:"批准工具失败"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/admin/tools/[id]/approve/route",pathname:"/api/admin/tools/[id]/approve",filename:"route",bundlePath:"app/api/admin/tools/[id]/approve/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:g,workUnitAsyncStorage:y,serverHooks:x}=m;function v(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:y})}},96487:()=>{}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,580],()=>r(83787));module.exports=i})();