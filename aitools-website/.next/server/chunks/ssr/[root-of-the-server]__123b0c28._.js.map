{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PerformanceOptimizations = registerClientReference(\n    function() { throw new Error(\"Attempted to call PerformanceOptimizations() from the server but PerformanceOptimizations is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/seo/PerformanceMonitor.tsx <module evaluation>\",\n    \"PerformanceOptimizations\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/seo/PerformanceMonitor.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/seo/PerformanceMonitor.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,2BAA2B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,2EACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6S,GAC1U,2EACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PerformanceOptimizations = registerClientReference(\n    function() { throw new Error(\"Attempted to call PerformanceOptimizations() from the server but PerformanceOptimizations is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/seo/PerformanceMonitor.tsx\",\n    \"PerformanceOptimizations\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/seo/PerformanceMonitor.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/seo/PerformanceMonitor.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,2BAA2B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,uDACA;uCAEW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyR,GACtT,uDACA", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport PerformanceMonitor from '@/components/seo/PerformanceMonitor';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <PerformanceMonitor />\n\n      {/* Main Content */}\n      <main className=\"flex-1\">\n        {children}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </div>\n              <p className=\"text-gray-600 mb-4\">\n                发现最新最好的 AI 工具，提升您的工作效率和创造力。\n              </p>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                快速链接\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href=\"/tools\" className=\"text-gray-600 hover:text-blue-600\">\n                    工具目录\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/categories\" className=\"text-gray-600 hover:text-blue-600\">\n                    分类浏览\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/submit\" className=\"text-gray-600 hover:text-blue-600\">\n                    提交工具\n                  </Link>\n                </li>\n              </ul>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                支持\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    帮助中心\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    联系我们\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    隐私政策\n                  </a>\n                </li>\n              </ul>\n            </div>\n          </div>\n          \n          <div className=\"border-t border-gray-200 mt-8 pt-8\">\n            <p className=\"text-center text-gray-600\">\n              © 2024 AI Tools Directory. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAMA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;IACjD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,+IAAA,CAAA,UAAkB;;;;;0BAGnB,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAIH,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,8OAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAoC;;;;;;;;;;;8DAIpE,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;kEAAoC;;;;;;;;;;;8DAIzE,8OAAC;8DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;8CAOzE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DACC,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,8OAAC;8DACC,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,8OAAC;8DACC,cAAA,8OAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;uCAEe", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryPageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/categories/CategoryPageClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/categories/CategoryPageClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoT,GACjV,kFACA", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryPageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/categories/CategoryPageClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/categories/CategoryPageClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgS,GAC7T,8DACA", "debugId": null}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error(\n    'Please define the MONGODB_URI environment variable inside .env.local'\n  );\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function dbConnect() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default dbConnect;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MACR;AAEJ;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 432, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface ITool extends Document {\n  name: string;\n  tagline?: string; // 工具标语/副标题\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  screenshots?: string[];\n  submittedBy: string; // User ID who submitted\n  submittedAt: Date;\n  publishedAt?: Date;\n  status: 'draft' | 'pending' | 'approved' | 'published' | 'rejected'; // 添加published状态\n  reviewNotes?: string;\n  reviewedBy?: string; // Admin ID who reviewed\n  reviewedAt?: Date;\n\n  // 发布日期选择相关\n  launchDateSelected?: boolean; // 是否已选择发布日期\n  selectedLaunchDate?: Date; // 用户选择的发布日期\n  launchOption?: 'free' | 'paid'; // 发布选项：免费或付费\n\n  // 付费相关\n  paymentRequired?: boolean; // 是否需要付费\n  paymentAmount?: number; // 付费金额（分为单位）\n  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded'; // 支付状态\n  orderId?: string; // 订单ID\n  paymentMethod?: string; // 支付方式\n  paidAt?: Date; // 支付完成时间\n\n  views: number;\n  likes: number;\n  likedBy: string[]; // 点赞用户ID列表\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst ToolSchema: Schema = new Schema({\n  name: {\n    type: String,\n    required: [true, 'Tool name is required'],\n    trim: true,\n    maxlength: [100, 'Tool name cannot exceed 100 characters']\n  },\n  tagline: {\n    type: String,\n    trim: true,\n    maxlength: [200, 'Tagline cannot exceed 200 characters']\n  },\n  description: {\n    type: String,\n    required: [true, 'Tool description is required'],\n    trim: true,\n    maxlength: [500, 'Description cannot exceed 500 characters']\n  },\n  longDescription: {\n    type: String,\n    trim: true,\n    maxlength: [2000, 'Long description cannot exceed 2000 characters']\n  },\n  website: {\n    type: String,\n    required: [true, 'Website URL is required'],\n    trim: true,\n    validate: {\n      validator: function(v: string) {\n        return /^https?:\\/\\/.+/.test(v);\n      },\n      message: 'Please enter a valid URL'\n    }\n  },\n  logo: {\n    type: String,\n    trim: true\n  },\n  category: {\n    type: String,\n    required: [true, 'Category is required'],\n    enum: [\n      'text-generation',\n      'image-generation', \n      'video-generation',\n      'audio-generation',\n      'code-generation',\n      'data-analysis',\n      'productivity',\n      'design',\n      'marketing',\n      'education',\n      'research',\n      'other'\n    ]\n  },\n  tags: [{\n    type: String,\n    trim: true,\n    lowercase: true\n  }],\n  pricing: {\n    type: String,\n    required: [true, 'Pricing model is required'],\n    enum: ['free', 'freemium', 'paid']\n  },\n  pricingDetails: {\n    type: String,\n    trim: true,\n    maxlength: [500, 'Pricing details cannot exceed 500 characters']\n  },\n  screenshots: [{\n    type: String,\n    trim: true\n  }],\n  submittedBy: {\n    type: String,\n    required: [true, 'Submitter ID is required'],\n    trim: true\n  },\n  submittedAt: {\n    type: Date,\n    default: Date.now\n  },\n  publishedAt: {\n    type: Date\n  },\n  status: {\n    type: String,\n    required: true,\n    enum: ['draft', 'pending', 'approved', 'published', 'rejected'],\n    default: 'draft'\n  },\n  reviewNotes: {\n    type: String,\n    trim: true,\n    maxlength: [1000, 'Review notes cannot exceed 1000 characters']\n  },\n  reviewedBy: {\n    type: String,\n    trim: true\n  },\n  reviewedAt: {\n    type: Date\n  },\n\n  // 发布日期选择相关\n  launchDateSelected: {\n    type: Boolean,\n    default: false\n  },\n  selectedLaunchDate: {\n    type: Date\n  },\n  launchOption: {\n    type: String,\n    enum: ['free', 'paid']\n  },\n\n  // 付费相关\n  paymentRequired: {\n    type: Boolean,\n    default: false\n  },\n  paymentAmount: {\n    type: Number,\n    min: 0\n  },\n  paymentStatus: {\n    type: String,\n    enum: ['pending', 'completed', 'failed', 'refunded']\n  },\n  orderId: {\n    type: String,\n    trim: true\n  },\n  paymentMethod: {\n    type: String,\n    trim: true\n  },\n  paidAt: {\n    type: Date\n  },\n  views: {\n    type: Number,\n    default: 0,\n    min: 0\n  },\n  likes: {\n    type: Number,\n    default: 0,\n    min: 0\n  },\n  likedBy: [{\n    type: String,\n    trim: true\n  }],\n  isActive: {\n    type: Boolean,\n    default: true\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes for better query performance\nToolSchema.index({ status: 1, isActive: 1 });\nToolSchema.index({ category: 1, status: 1 });\nToolSchema.index({ tags: 1, status: 1 });\nToolSchema.index({ submittedBy: 1 });\nToolSchema.index({ publishedAt: -1 });\nToolSchema.index({ views: -1 });\nToolSchema.index({ likes: -1 });\n\n// Text search index\nToolSchema.index({\n  name: 'text',\n  tagline: 'text',\n  description: 'text',\n  longDescription: 'text',\n  tags: 'text'\n});\n\nexport default mongoose.models.Tool || mongoose.model<ITool>('Tool', ToolSchema);\n"], "names": [], "mappings": ";;;AAAA;;AA2CA,MAAM,aAAqB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACpC,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;QACzC,MAAM;QACN,WAAW;YAAC;YAAK;SAAyC;IAC5D;IACA,SAAS;QACP,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAK;SAAuC;IAC1D;IACA,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA+B;QAChD,MAAM;QACN,WAAW;YAAC;YAAK;SAA2C;IAC9D;IACA,iBAAiB;QACf,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAAiD;IACrE;IACA,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA0B;QAC3C,MAAM;QACN,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,iBAAiB,IAAI,CAAC;YAC/B;YACA,SAAS;QACX;IACF;IACA,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,MAAM;QAAC;YACL,MAAM;YACN,MAAM;YACN,WAAW;QACb;KAAE;IACF,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA4B;QAC7C,MAAM;YAAC;YAAQ;YAAY;SAAO;IACpC;IACA,gBAAgB;QACd,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAK;SAA+C;IAClE;IACA,aAAa;QAAC;YACZ,MAAM;YACN,MAAM;QACR;KAAE;IACF,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;IACA,aAAa;QACX,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAS;YAAW;YAAY;YAAa;SAAW;QAC/D,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAA6C;IACjE;IACA,YAAY;QACV,MAAM;QACN,MAAM;IACR;IACA,YAAY;QACV,MAAM;IACR;IAEA,WAAW;IACX,oBAAoB;QAClB,MAAM;QACN,SAAS;IACX;IACA,oBAAoB;QAClB,MAAM;IACR;IACA,cAAc;QACZ,MAAM;QACN,MAAM;YAAC;YAAQ;SAAO;IACxB;IAEA,OAAO;IACP,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,eAAe;QACb,MAAM;QACN,KAAK;IACP;IACA,eAAe;QACb,MAAM;QACN,MAAM;YAAC;YAAW;YAAa;YAAU;SAAW;IACtD;IACA,SAAS;QACP,MAAM;QACN,MAAM;IACR;IACA,eAAe;QACb,MAAM;QACN,MAAM;IACR;IACA,QAAQ;QACN,MAAM;IACR;IACA,OAAO;QACL,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,OAAO;QACL,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,SAAS;QAAC;YACR,MAAM;YACN,MAAM;QACR;KAAE;IACF,UAAU;QACR,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,uCAAuC;AACvC,WAAW,KAAK,CAAC;IAAE,QAAQ;IAAG,UAAU;AAAE;AAC1C,WAAW,KAAK,CAAC;IAAE,UAAU;IAAG,QAAQ;AAAE;AAC1C,WAAW,KAAK,CAAC;IAAE,MAAM;IAAG,QAAQ;AAAE;AACtC,WAAW,KAAK,CAAC;IAAE,aAAa;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,aAAa,CAAC;AAAE;AACnC,WAAW,KAAK,CAAC;IAAE,OAAO,CAAC;AAAE;AAC7B,WAAW,KAAK,CAAC;IAAE,OAAO,CAAC;AAAE;AAE7B,oBAAoB;AACpB,WAAW,KAAK,CAAC;IACf,MAAM;IACN,SAAS;IACT,aAAa;IACb,iBAAiB;IACjB,MAAM;AACR;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 705, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/categories/%5Bslug%5D/page.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Metadata } from 'next';\nimport { notFound } from 'next/navigation';\nimport Layout from '@/components/Layout';\nimport CategoryPageClient from '@/components/categories/CategoryPageClient';\nimport dbConnect from '@/lib/mongodb';\nimport Tool from '@/models/Tool';\nimport { ArrowLeft } from 'lucide-react';\n\n// 分类信息接口\ninterface CategoryInfo {\n  _id: string;\n  name: string;\n  slug: string;\n  description: string;\n  icon: string;\n  color: string;\n  toolCount: number;\n}\n\n// 分类元数据配置（与分类页面保持一致）\nfunction getCategoryMetadata(categoryId: string) {\n  const categoryMetadata: Record<string, { description: string; icon: string; color: string }> = {\n    'text-generation': {\n      description: '基于AI的文本生成工具，包括写作助手、内容创作、文案生成等',\n      icon: '✍️',\n      color: '#3B82F6'\n    },\n    'image-generation': {\n      description: 'AI图像生成和编辑工具，包括图片创作、图像处理、艺术生成等',\n      icon: '🎨',\n      color: '#8B5CF6'\n    },\n    'code-generation': {\n      description: 'AI代码生成和编程辅助工具，包括代码补全、代码审查、编程助手等',\n      icon: '💻',\n      color: '#10B981'\n    },\n    'data-analysis': {\n      description: 'AI数据分析和处理工具，包括数据挖掘、统计分析、可视化等',\n      icon: '📊',\n      color: '#F59E0B'\n    },\n    'audio-processing': {\n      description: 'AI音频处理工具，包括语音识别、音频编辑、音乐生成等',\n      icon: '🎵',\n      color: '#EF4444'\n    },\n    'video-editing': {\n      description: 'AI视频编辑和处理工具，包括视频剪辑、特效制作、视频生成等',\n      icon: '🎬',\n      color: '#06B6D4'\n    },\n    'translation': {\n      description: '多语言翻译和本地化工具，打破语言障碍',\n      icon: '🌐',\n      color: '#84CC16'\n    },\n    'search-engines': {\n      description: '智能搜索引擎和信息检索工具',\n      icon: '🔍',\n      color: '#6366F1'\n    },\n    'education': {\n      description: '教育学习辅助工具，个性化学习体验',\n      icon: '📚',\n      color: '#EC4899'\n    },\n    'marketing': {\n      description: '营销自动化、内容营销、广告优化等营销工具',\n      icon: '📈',\n      color: '#F97316'\n    },\n    'productivity': {\n      description: '提高工作效率的各类生产力工具',\n      icon: '⚡',\n      color: '#22D3EE'\n    },\n    'customer-service': {\n      description: '客户服务自动化、聊天机器人等客服工具',\n      icon: '🤝',\n      color: '#A855F7'\n    }\n  };\n\n  return categoryMetadata[categoryId] || {\n    description: '优秀的AI工具分类',\n    icon: '🤖',\n    color: '#6B7280'\n  };\n}\n\n// 获取分类显示名称\nfunction getCategoryDisplayName(categoryId: string): string {\n  const displayNames: Record<string, string> = {\n    'text-generation': '文本生成',\n    'image-generation': '图像生成',\n    'code-generation': '代码生成',\n    'data-analysis': '数据分析',\n    'audio-processing': '音频处理',\n    'video-editing': '视频编辑',\n    'translation': '翻译工具',\n    'search-engines': '搜索引擎',\n    'education': '教育学习',\n    'marketing': '营销工具',\n    'productivity': '生产力工具',\n    'customer-service': '客户服务'\n  };\n\n  return displayNames[categoryId] || categoryId;\n}\n\n// 生成动态metadata\nexport async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {\n  const { slug } = await params;\n  const metadata = getCategoryMetadata(slug);\n  const displayName = getCategoryDisplayName(slug);\n\n  return {\n    title: `${displayName} - AI工具分类`,\n    description: metadata.description,\n    keywords: `${displayName}, AI工具, 人工智能, ${slug}`,\n    openGraph: {\n      title: `${displayName} - AI工具分类`,\n      description: metadata.description,\n      type: 'website',\n    },\n  };\n}\n\n// 服务端获取分类数据\nasync function getCategoryData(slug: string) {\n  try {\n    await dbConnect();\n\n    // 获取该分类下的工具\n    const tools = await Tool.find({\n      category: slug,\n      status: 'published'\n    })\n    .select('_id name description logo pricing views likes category tags createdAt publishedAt')\n    .sort({ publishedAt: -1 })\n    .lean();\n\n    // 序列化数据\n    const serializedTools = tools.map(tool => ({\n      ...tool,\n      _id: tool._id.toString(),\n      createdAt: tool.createdAt?.toISOString(),\n      publishedAt: tool.publishedAt?.toISOString(),\n    }));\n\n    // 生成分类信息\n    const metadata = getCategoryMetadata(slug);\n    const categoryInfo: CategoryInfo = {\n      _id: slug,\n      name: getCategoryDisplayName(slug),\n      slug: slug,\n      description: metadata.description,\n      icon: metadata.icon,\n      color: metadata.color,\n      toolCount: tools.length\n    };\n\n    return {\n      categoryInfo,\n      tools: serializedTools,\n      error: null\n    };\n  } catch (error) {\n    console.error('Error fetching category data:', error);\n    return {\n      categoryInfo: null,\n      tools: [],\n      error: '获取分类数据失败，请稍后重试'\n    };\n  }\n}\n\n// 服务端渲染的分类详情页面\nexport default async function CategoryPage({ params }: { params: Promise<{ slug: string }> }) {\n  const { slug } = await params;\n  const { categoryInfo, tools, error } = await getCategoryData(slug);\n\n  // 如果分类不存在，返回404\n  if (!categoryInfo && !error) {\n    notFound();\n  }\n\n  if (error) {\n    return (\n      <Layout>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center py-12\">\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">加载失败</h1>\n            <p className=\"text-gray-600\">{error}</p>\n            <Link\n              href=\"/categories\"\n              className=\"inline-flex items-center mt-4 text-blue-600 hover:text-blue-800\"\n            >\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              返回分类列表\n            </Link>\n          </div>\n        </div>\n      </Layout>\n    );\n  }\n\n  return (\n    <Layout>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* 面包屑导航 - 服务端渲染 */}\n        <nav className=\"flex items-center space-x-2 text-sm text-gray-600 mb-6\">\n          <Link href=\"/\" className=\"hover:text-gray-900\">首页</Link>\n          <span>/</span>\n          <Link href=\"/categories\" className=\"hover:text-gray-900\">分类</Link>\n          <span>/</span>\n          <span className=\"text-gray-900\">{categoryInfo?.name}</span>\n        </nav>\n\n        {/* 分类头部信息 - 服务端渲染 */}\n        {categoryInfo && (\n          <div className=\"mb-8\">\n            <div className=\"flex items-center justify-between mb-6\">\n              <Link\n                href=\"/categories\"\n                className=\"inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors\"\n              >\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                返回分类列表\n              </Link>\n            </div>\n\n            <div className=\"bg-white rounded-lg border border-gray-200 p-6 mb-8\">\n              <div className=\"flex items-center space-x-4 mb-4\">\n                <div\n                  className=\"w-16 h-16 rounded-lg flex items-center justify-center text-2xl\"\n                  style={{ backgroundColor: categoryInfo.color + '20' }}\n                >\n                  {categoryInfo.icon}\n                </div>\n                <div>\n                  <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                    {categoryInfo.name}\n                  </h1>\n                  <p className=\"text-gray-600 text-lg\">\n                    {categoryInfo.description}\n                  </p>\n                </div>\n              </div>\n              <div className=\"text-sm text-gray-500\">\n                共 {categoryInfo.toolCount} 个工具\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* 工具列表 - 客户端组件处理交互 */}\n        <CategoryPageClient\n          initialTools={tools}\n        />\n\n        {/* 相关分类 */}\n        <section className=\"mt-16 bg-gray-50 rounded-lg p-8\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">相关分类</h2>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n            <Link\n              href=\"/categories/image-generation\"\n              className=\"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow\"\n            >\n              <span className=\"text-2xl\">🎨</span>\n              <span className=\"text-sm font-medium text-gray-900\">图像生成</span>\n            </Link>\n            <Link\n              href=\"/categories/code-generation\"\n              className=\"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow\"\n            >\n              <span className=\"text-2xl\">💻</span>\n              <span className=\"text-sm font-medium text-gray-900\">代码生成</span>\n            </Link>\n            <Link\n              href=\"/categories/data-analysis\"\n              className=\"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow\"\n            >\n              <span className=\"text-2xl\">📊</span>\n              <span className=\"text-sm font-medium text-gray-900\">数据分析</span>\n            </Link>\n            <Link\n              href=\"/categories/audio-processing\"\n              className=\"flex items-center space-x-2 p-3 bg-white rounded-lg hover:shadow-sm transition-shadow\"\n            >\n              <span className=\"text-2xl\">🎵</span>\n              <span className=\"text-sm font-medium text-gray-900\">音频处理</span>\n            </Link>\n          </div>\n        </section>\n      </div>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAaA,qBAAqB;AACrB,SAAS,oBAAoB,UAAkB;IAC7C,MAAM,mBAAyF;QAC7F,mBAAmB;YACjB,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA,oBAAoB;YAClB,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA,mBAAmB;YACjB,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA,iBAAiB;YACf,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA,oBAAoB;YAClB,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA,iBAAiB;YACf,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA,eAAe;YACb,aAAa;YAC<PERSON>,MAAM;YACN,OAAO;QACT;QACA,kBAAkB;YAChB,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA,aAAa;YACX,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA,aAAa;YACX,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA,gBAAgB;YACd,aAAa;YACb,MAAM;YACN,OAAO;QACT;QACA,oBAAoB;YAClB,aAAa;YACb,MAAM;YACN,OAAO;QACT;IACF;IAEA,OAAO,gBAAgB,CAAC,WAAW,IAAI;QACrC,aAAa;QACb,MAAM;QACN,OAAO;IACT;AACF;AAEA,WAAW;AACX,SAAS,uBAAuB,UAAkB;IAChD,MAAM,eAAuC;QAC3C,mBAAmB;QACnB,oBAAoB;QACpB,mBAAmB;QACnB,iBAAiB;QACjB,oBAAoB;QACpB,iBAAiB;QACjB,eAAe;QACf,kBAAkB;QAClB,aAAa;QACb,aAAa;QACb,gBAAgB;QAChB,oBAAoB;IACtB;IAEA,OAAO,YAAY,CAAC,WAAW,IAAI;AACrC;AAGO,eAAe,iBAAiB,EAAE,MAAM,EAAyC;IACtF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,WAAW,oBAAoB;IACrC,MAAM,cAAc,uBAAuB;IAE3C,OAAO;QACL,OAAO,GAAG,YAAY,SAAS,CAAC;QAChC,aAAa,SAAS,WAAW;QACjC,UAAU,GAAG,YAAY,cAAc,EAAE,MAAM;QAC/C,WAAW;YACT,OAAO,GAAG,YAAY,SAAS,CAAC;YAChC,aAAa,SAAS,WAAW;YACjC,MAAM;QACR;IACF;AACF;AAEA,YAAY;AACZ,eAAe,gBAAgB,IAAY;IACzC,IAAI;QACF,MAAM,CAAA,GAAA,qHAAA,CAAA,UAAS,AAAD;QAEd,YAAY;QACZ,MAAM,QAAQ,MAAM,qHAAA,CAAA,UAAI,CAAC,IAAI,CAAC;YAC5B,UAAU;YACV,QAAQ;QACV,GACC,MAAM,CAAC,qFACP,IAAI,CAAC;YAAE,aAAa,CAAC;QAAE,GACvB,IAAI;QAEL,QAAQ;QACR,MAAM,kBAAkB,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACzC,GAAG,IAAI;gBACP,KAAK,KAAK,GAAG,CAAC,QAAQ;gBACtB,WAAW,KAAK,SAAS,EAAE;gBAC3B,aAAa,KAAK,WAAW,EAAE;YACjC,CAAC;QAED,SAAS;QACT,MAAM,WAAW,oBAAoB;QACrC,MAAM,eAA6B;YACjC,KAAK;YACL,MAAM,uBAAuB;YAC7B,MAAM;YACN,aAAa,SAAS,WAAW;YACjC,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,KAAK;YACrB,WAAW,MAAM,MAAM;QACzB;QAEA,OAAO;YACL;YACA,OAAO;YACP,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;YACL,cAAc;YACd,OAAO,EAAE;YACT,OAAO;QACT;IACF;AACF;AAGe,eAAe,aAAa,EAAE,MAAM,EAAyC;IAC1F,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,gBAAgB;IAE7D,gBAAgB;IAChB,IAAI,CAAC,gBAAgB,CAAC,OAAO;QAC3B,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,4HAAA,CAAA,UAAM;sBACL,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAiB;;;;;;sCAC9B,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;IAOlD;IAEA,qBACE,8OAAC,4HAAA,CAAA,UAAM;kBACL,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAsB;;;;;;sCAC/C,8OAAC;sCAAK;;;;;;sCACN,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAc,WAAU;sCAAsB;;;;;;sCACzD,8OAAC;sCAAK;;;;;;sCACN,8OAAC;4BAAK,WAAU;sCAAiB,cAAc;;;;;;;;;;;;gBAIhD,8BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAK1C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,iBAAiB,aAAa,KAAK,GAAG;4CAAK;sDAEnD,aAAa,IAAI;;;;;;sDAEpB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DACX,aAAa,IAAI;;;;;;8DAEpB,8OAAC;oDAAE,WAAU;8DACV,aAAa,WAAW;;;;;;;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;wCAAwB;wCAClC,aAAa,SAAS;wCAAC;;;;;;;;;;;;;;;;;;;8BAOlC,8OAAC,sJAAA,CAAA,UAAkB;oBACjB,cAAc;;;;;;8BAIhB,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAW;;;;;;sDAC3B,8OAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;8CAEtD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAW;;;;;;sDAC3B,8OAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;8CAEtD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAW;;;;;;sDAC3B,8OAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;8CAEtD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAW;;;;;;sDAC3B,8OAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlE", "debugId": null}}]}