{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\ninterface PerformanceMetrics {\n  fcp?: number; // First Contentful Paint\n  lcp?: number; // Largest Contentful Paint\n  fid?: number; // First Input Delay\n  cls?: number; // Cumulative Layout Shift\n  ttfb?: number; // Time to First Byte\n}\n\nexport default function PerformanceMonitor() {\n  useEffect(() => {\n    // 只在生产环境中启用性能监控\n    if (process.env.NODE_ENV !== 'production') {\n      return;\n    }\n\n    const metrics: PerformanceMetrics = {};\n\n    // 监控 First Contentful Paint (FCP)\n    const observeFCP = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');\n        if (fcpEntry) {\n          metrics.fcp = fcpEntry.startTime;\n          reportMetric('FCP', fcpEntry.startTime);\n        }\n      });\n      observer.observe({ entryTypes: ['paint'] });\n    };\n\n    // 监控 Largest Contentful Paint (LCP)\n    const observeLCP = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        const lastEntry = entries[entries.length - 1];\n        metrics.lcp = lastEntry.startTime;\n        reportMetric('LCP', lastEntry.startTime);\n      });\n      observer.observe({ entryTypes: ['largest-contentful-paint'] });\n    };\n\n    // 监控 First Input Delay (FID)\n    const observeFID = () => {\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        entries.forEach((entry: any) => {\n          metrics.fid = entry.processingStart - entry.startTime;\n          reportMetric('FID', entry.processingStart - entry.startTime);\n        });\n      });\n      observer.observe({ entryTypes: ['first-input'] });\n    };\n\n    // 监控 Cumulative Layout Shift (CLS)\n    const observeCLS = () => {\n      let clsValue = 0;\n      const observer = new PerformanceObserver((list) => {\n        const entries = list.getEntries();\n        entries.forEach((entry: any) => {\n          if (!entry.hadRecentInput) {\n            clsValue += entry.value;\n          }\n        });\n        metrics.cls = clsValue;\n        reportMetric('CLS', clsValue);\n      });\n      observer.observe({ entryTypes: ['layout-shift'] });\n    };\n\n    // 监控 Time to First Byte (TTFB)\n    const observeTTFB = () => {\n      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;\n      if (navigationEntry) {\n        const ttfb = navigationEntry.responseStart - navigationEntry.requestStart;\n        metrics.ttfb = ttfb;\n        reportMetric('TTFB', ttfb);\n      }\n    };\n\n    // 报告性能指标\n    const reportMetric = (name: string, value: number) => {\n      // 在开发环境中输出到控制台\n      if (process.env.NODE_ENV === 'development') {\n        console.log(`Performance Metric - ${name}:`, value);\n      }\n\n      // 在生产环境中可以发送到分析服务\n      // 例如 Google Analytics, Vercel Analytics 等\n      if (typeof window !== 'undefined' && window.gtag) {\n        window.gtag('event', 'web_vitals', {\n          event_category: 'Performance',\n          event_label: name,\n          value: Math.round(value),\n          non_interaction: true,\n        });\n      }\n    };\n\n    // 检查浏览器支持\n    if (typeof PerformanceObserver !== 'undefined') {\n      observeFCP();\n      observeLCP();\n      observeFID();\n      observeCLS();\n    }\n\n    observeTTFB();\n\n    // 页面卸载时报告最终指标\n    const reportFinalMetrics = () => {\n      if (Object.keys(metrics).length > 0) {\n        // 可以发送到分析服务\n        console.log('Final Performance Metrics:', metrics);\n      }\n    };\n\n    window.addEventListener('beforeunload', reportFinalMetrics);\n\n    return () => {\n      window.removeEventListener('beforeunload', reportFinalMetrics);\n    };\n  }, []);\n\n  return null; // 这是一个无UI的监控组件\n}\n\n// 性能优化建议\nexport const PerformanceOptimizations = {\n  // FCP 优化建议\n  fcp: {\n    good: 1800, // < 1.8s\n    needsImprovement: 3000, // 1.8s - 3s\n    suggestions: [\n      '减少服务器响应时间',\n      '消除阻塞渲染的资源',\n      '压缩CSS和JavaScript',\n      '使用CDN加速资源加载',\n    ],\n  },\n  \n  // LCP 优化建议\n  lcp: {\n    good: 2500, // < 2.5s\n    needsImprovement: 4000, // 2.5s - 4s\n    suggestions: [\n      '优化图片加载',\n      '预加载关键资源',\n      '减少JavaScript执行时间',\n      '使用服务端渲染',\n    ],\n  },\n  \n  // FID 优化建议\n  fid: {\n    good: 100, // < 100ms\n    needsImprovement: 300, // 100ms - 300ms\n    suggestions: [\n      '减少JavaScript执行时间',\n      '分割长任务',\n      '使用Web Workers',\n      '延迟加载非关键JavaScript',\n    ],\n  },\n  \n  // CLS 优化建议\n  cls: {\n    good: 0.1, // < 0.1\n    needsImprovement: 0.25, // 0.1 - 0.25\n    suggestions: [\n      '为图片和视频设置尺寸属性',\n      '避免在现有内容上方插入内容',\n      '使用transform动画而非改变布局的动画',\n      '预留广告位空间',\n    ],\n  },\n};\n\n// 声明全局gtag类型\ndeclare global {\n  interface Window {\n    gtag?: (...args: any[]) => void;\n  }\n}\n"], "names": [], "mappings": ";;;;AAeQ;AAbR;;AAFA;;AAYe,SAAS;;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,gBAAgB;YAChB,wCAA2C;gBACzC;YACF;;YAEA,MAAM;YAEN,kCAAkC;YAClC,MAAM;YAYN,oCAAoC;YACpC,MAAM;YAUN,6BAA6B;YAC7B,MAAM;YAWN,mCAAmC;YACnC,MAAM;YAeN,+BAA+B;YAC/B,MAAM;YASN,SAAS;YACT,MAAM;YA4BN,cAAc;YACd,MAAM;QAYR;uCAAG,EAAE;IAEL,OAAO,MAAM,eAAe;AAC9B;GApHwB;KAAA;AAuHjB,MAAM,2BAA2B;IACtC,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;IAEA,WAAW;IACX,KAAK;QACH,MAAM;QACN,kBAAkB;QAClB,aAAa;YACX;YACA;YACA;YACA;SACD;IACH;AACF", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport PerformanceMonitor from '@/components/seo/PerformanceMonitor';\n\ninterface LayoutProps {\n  children: React.ReactNode;\n}\n\nconst Layout: React.FC<LayoutProps> = ({ children }) => {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <PerformanceMonitor />\n\n      {/* Main Content */}\n      <main className=\"flex-1\">\n        {children}\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"col-span-1 md:col-span-2\">\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">AI</span>\n                </div>\n                <span className=\"text-xl font-bold text-gray-900\">AI Tools</span>\n              </div>\n              <p className=\"text-gray-600 mb-4\">\n                发现最新最好的 AI 工具，提升您的工作效率和创造力。\n              </p>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                快速链接\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <Link href=\"/tools\" className=\"text-gray-600 hover:text-blue-600\">\n                    工具目录\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/categories\" className=\"text-gray-600 hover:text-blue-600\">\n                    分类浏览\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/submit\" className=\"text-gray-600 hover:text-blue-600\">\n                    提交工具\n                  </Link>\n                </li>\n              </ul>\n            </div>\n            \n            <div>\n              <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n                支持\n              </h3>\n              <ul className=\"space-y-2\">\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    帮助中心\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    联系我们\n                  </a>\n                </li>\n                <li>\n                  <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                    隐私政策\n                  </a>\n                </li>\n              </ul>\n            </div>\n          </div>\n          \n          <div className=\"border-t border-gray-200 mt-8 pt-8\">\n            <p className=\"text-center text-gray-600\">\n              © 2024 AI Tools Directory. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default Layout;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAMA,MAAM,SAAgC,CAAC,EAAE,QAAQ,EAAE;IACjD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,kJAAA,CAAA,UAAkB;;;;;0BAGnB,6LAAC;gBAAK,WAAU;0BACb;;;;;;0BAIH,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;;;;;;;sDAEpD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAoC;;;;;;;;;;;8DAIpE,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;kEAAoC;;;;;;;;;;;8DAIzE,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;8CAOzE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;sDAGlF,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;8DAI5D,6LAAC;8DACC,cAAA,6LAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;KAlFM;uCAoFS", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx"], "sourcesContent": ["'use client';\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport default function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12'\n  };\n\n  return (\n    <div className={`flex justify-center items-center ${className}`}>\n      <div className={`${sizeClasses[size]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`}></div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAOe,SAAS,eAAe,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAuB;IACzF,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC7D,cAAA,6LAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,qEAAqE,CAAC;;;;;;;;;;;AAGjH;KAZwB", "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { AlertCircle, X } from 'lucide-react';\n\ninterface ErrorMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function ErrorMessage({ message, onClose, className = '' }: ErrorMessageProps) {\n  return (\n    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <AlertCircle className=\"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-red-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-red-400 hover:text-red-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,aAAa,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAqB;IAC1F,qBACE,6LAAC;QAAI,WAAW,CAAC,+CAA+C,EAAE,WAAW;kBAC3E,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;gBAEtC,yBACC,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB;KAnBwB", "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx"], "sourcesContent": ["'use client';\n\nimport { CheckCircle, X } from 'lucide-react';\n\ninterface SuccessMessageProps {\n  message: string;\n  onClose?: () => void;\n  className?: string;\n}\n\nexport default function SuccessMessage({ message, onClose, className = '' }: SuccessMessageProps) {\n  return (\n    <div className={`bg-green-50 border border-green-200 rounded-lg p-4 ${className}`}>\n      <div className=\"flex items-start\">\n        <CheckCircle className=\"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0\" />\n        <div className=\"flex-1\">\n          <p className=\"text-green-800 text-sm\">{message}</p>\n        </div>\n        {onClose && (\n          <button\n            onClick={onClose}\n            className=\"ml-3 text-green-400 hover:text-green-600 transition-colors\"\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAUe,SAAS,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,EAAuB;IAC9F,qBACE,6LAAC;QAAI,WAAW,CAAC,mDAAmD,EAAE,WAAW;kBAC/E,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;gBAExC,yBACC,6LAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMzB;KAnBwB", "debugId": null}}, {"offset": {"line": 569, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts"], "sourcesContent": ["/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */\n\n// 基础价格配置\nexport const PRICING_CONFIG = {\n  // 优先发布服务价格\n  PRIORITY_LAUNCH: {\n    // 显示价格（元）\n    displayPrice: 19.9,\n    // Stripe价格（分为单位）\n    stripeAmount: 1990,\n    // 货币\n    currency: 'USD',\n    // Stripe货币代码（小写）\n    stripeCurrency: 'usd', // 注意：当前使用USD进行测试\n    // 产品名称\n    productName: 'AI工具优先发布服务',\n    // 产品描述\n    description: '让您的AI工具获得优先审核和推荐位置',\n    // 功能特性\n    features: [\n      '可选择任意发布日期',\n      '优先审核处理',\n      '首页推荐位置',\n      '专属客服支持'\n    ]\n  },\n  \n  // 免费发布配置\n  FREE_LAUNCH: {\n    displayPrice: 0,\n    stripeAmount: 0,\n    currency: 'USD',\n    stripeCurrency: 'usd',\n    productName: '免费发布服务',\n    description: '选择一个月后的任意发布日期',\n    features: [\n      '免费提交审核',\n      '发布日期：一个月后起',\n      '正常审核流程',\n      '标准展示位置'\n    ]\n  }\n} as const;\n\n// 发布选项配置\nexport const LAUNCH_OPTIONS = [\n  {\n    id: 'free' as const,\n    title: '免费发布',\n    description: PRICING_CONFIG.FREE_LAUNCH.description,\n    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.FREE_LAUNCH.features\n  },\n  {\n    id: 'paid' as const,\n    title: '优先发布',\n    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n    recommended: true\n  }\n] as const;\n\n// 工具定价类型配置\nexport const TOOL_PRICING_TYPES = {\n  FREE: {\n    value: 'free',\n    label: '免费',\n    color: 'bg-green-100 text-green-800'\n  },\n  FREEMIUM: {\n    value: 'freemium',\n    label: '免费增值',\n    color: 'bg-blue-100 text-blue-800'\n  },\n  PAID: {\n    value: 'paid',\n    label: '付费',\n    color: 'bg-orange-100 text-orange-800'\n  }\n} as const;\n\n// 工具定价选项（用于筛选）\nexport const TOOL_PRICING_OPTIONS = [\n  { value: '', label: '所有价格' },\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 工具定价选项（用于表单）\nexport const TOOL_PRICING_FORM_OPTIONS = [\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 类型定义\nexport type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];\nexport type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];\n\n// 辅助函数\nexport const getPricingConfig = (optionId: LaunchOptionId) => {\n  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\n\nexport const getToolPricingColor = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.color;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.color;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.color;\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getToolPricingText = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.label;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.label;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.label;\n    default:\n      return pricing;\n  }\n};\n\n// 格式化价格显示\nexport const formatPrice = (price: number) => {\n  return price === 0 ? '免费' : `¥${price}`;\n};\n\n// 格式化Stripe金额显示\nexport const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,SAAS;;;;;;;;;;;;;AACF,MAAM,iBAAiB;IAC5B,WAAW;IACX,iBAAiB;QACf,UAAU;QACV,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,KAAK;QACL,UAAU;QACV,iBAAiB;QACjB,gBAAgB;QAChB,OAAO;QACP,aAAa;QACb,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,SAAS;IACT,aAAa;QACX,cAAc;QACd,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,WAAW,CAAC,WAAW;QACnD,OAAO,eAAe,WAAW,CAAC,YAAY;QAC9C,UAAU,eAAe,WAAW,CAAC,QAAQ;IAC/C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,eAAe,CAAC,WAAW;QACvD,OAAO,eAAe,eAAe,CAAC,YAAY;QAClD,UAAU,eAAe,eAAe,CAAC,QAAQ;QACjD,aAAa;IACf;CACD;AAGM,MAAM,qBAAqB;IAChC,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,uBAAuB;IAClC;QAAE,OAAO;QAAI,OAAO;IAAO;IAC3B;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAGM,MAAM,4BAA4B;IACvC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAOM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,SAAS,eAAe,eAAe,GAAG,eAAe,WAAW;AAC1F;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAGO,MAAM,cAAc,CAAC;IAC1B,OAAO,UAAU,IAAI,OAAO,CAAC,CAAC,EAAE,OAAO;AACzC;AAGO,MAAM,qBAAqB,CAAC,QAAgB,WAAmB,KAAK;IACzE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB", "debugId": null}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts"], "sourcesContent": ["// AI工具应用的预定义标签列表 - 精选最流行的50个标签\nexport const AVAILABLE_TAGS = [\n  // 核心AI功能\n  'AI助手', 'ChatGPT', '对话AI', '智能问答', '语言模型',\n\n  // 内容创作\n  '写作助手', '内容生成', '文案创作', '博客写作', '营销文案',\n\n  // 图像处理\n  '图像生成', '图像编辑', 'AI绘画', '头像生成', '背景移除',\n\n  // 视频处理\n  '视频生成', '视频编辑', '视频剪辑', '短视频制作', '视频字幕',\n\n  // 音频处理\n  '语音合成', '语音识别', '音乐生成', '语音转文字', '文字转语音',\n\n  // 代码开发\n  '代码生成', '代码补全', '代码审查', '开发助手', '低代码平台',\n\n  // 数据分析\n  '数据分析', '数据可视化', '商业智能', '机器学习', '深度学习',\n\n  // 办公效率\n  '办公自动化', '文档处理', '项目管理', '团队协作', '笔记工具',\n\n  // 设计工具\n  'UI设计', 'Logo设计', '网页设计', '平面设计', '原型设计',\n\n  // 营销工具\n  'SEO优化', '社交媒体营销', '邮件营销', '内容营销', '市场分析',\n\n  // 翻译工具\n  '机器翻译', '实时翻译', '文档翻译', '语音翻译'\n];\n\n// 标签的最大选择数量\nexport const MAX_TAGS_COUNT = 3;\n\n// 按分类组织的标签（用于更好的用户体验）\nexport const TAGS_BY_CATEGORY = {\n  '核心AI功能': ['AI助手', 'ChatGPT', '对话AI', '智能问答', '语言模型'],\n  '内容创作': ['写作助手', '内容生成', '文案创作', '博客写作', '营销文案'],\n  '图像处理': ['图像生成', '图像编辑', 'AI绘画', '头像生成', '背景移除'],\n  '视频处理': ['视频生成', '视频编辑', '视频剪辑', '短视频制作', '视频字幕'],\n  '音频处理': ['语音合成', '语音识别', '音乐生成', '语音转文字', '文字转语音'],\n  '代码开发': ['代码生成', '代码补全', '代码审查', '开发助手', '低代码平台'],\n  '数据分析': ['数据分析', '数据可视化', '商业智能', '机器学习', '深度学习'],\n  '办公效率': ['办公自动化', '文档处理', '项目管理', '团队协作', '笔记工具'],\n  '设计工具': ['UI设计', 'Logo设计', '网页设计', '平面设计', '原型设计'],\n  '营销工具': ['SEO优化', '社交媒体营销', '邮件营销', '内容营销', '市场分析'],\n  '翻译工具': ['机器翻译', '实时翻译', '文档翻译', '语音翻译']\n};\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;;AACxB,MAAM,iBAAiB;IAC5B,SAAS;IACT;IAAQ;IAAW;IAAQ;IAAQ;IAEnC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAQ;IAEhC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAQ;IAEhC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAS;IAEjC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAS;IAEjC,OAAO;IACP;IAAQ;IAAQ;IAAQ;IAAQ;IAEhC,OAAO;IACP;IAAQ;IAAS;IAAQ;IAAQ;IAEjC,OAAO;IACP;IAAS;IAAQ;IAAQ;IAAQ;IAEjC,OAAO;IACP;IAAQ;IAAU;IAAQ;IAAQ;IAElC,OAAO;IACP;IAAS;IAAU;IAAQ;IAAQ;IAEnC,OAAO;IACP;IAAQ;IAAQ;IAAQ;CACzB;AAGM,MAAM,iBAAiB;AAGvB,MAAM,mBAAmB;IAC9B,UAAU;QAAC;QAAQ;QAAW;QAAQ;QAAQ;KAAO;IACrD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAChD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;KAAO;IAChD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAS;KAAO;IACjD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAS;KAAQ;IAClD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;QAAQ;KAAQ;IACjD,QAAQ;QAAC;QAAQ;QAAS;QAAQ;QAAQ;KAAO;IACjD,QAAQ;QAAC;QAAS;QAAQ;QAAQ;QAAQ;KAAO;IACjD,QAAQ;QAAC;QAAQ;QAAU;QAAQ;QAAQ;KAAO;IAClD,QAAQ;QAAC;QAAS;QAAU;QAAQ;QAAQ;KAAO;IACnD,QAAQ;QAAC;QAAQ;QAAQ;QAAQ;KAAO;AAC1C", "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Tag, Search, X } from 'lucide-react';\nimport { AVAILABLE_TAGS, MAX_TAGS_COUNT } from '@/constants/tags';\n\ninterface TagSelectorProps {\n  selectedTags: string[];\n  onTagsChange: (tags: string[]) => void;\n  maxTags?: number;\n}\n\nexport default function TagSelector({\n  selectedTags,\n  onTagsChange,\n  maxTags = MAX_TAGS_COUNT\n}: TagSelectorProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [isOpen, setIsOpen] = useState(false);\n\n  const toggleTag = (tag: string) => {\n    if (selectedTags.includes(tag)) {\n      onTagsChange(selectedTags.filter(t => t !== tag));\n    } else if (selectedTags.length < maxTags) {\n      onTagsChange([...selectedTags, tag]);\n    }\n  };\n\n  const removeTag = (tag: string) => {\n    onTagsChange(selectedTags.filter(t => t !== tag));\n  };\n\n  // 过滤标签：根据搜索词过滤，并排除已选择的标签\n  const filteredTags = AVAILABLE_TAGS.filter(tag =>\n    tag.toLowerCase().includes(searchTerm.toLowerCase()) &&\n    !selectedTags.includes(tag)\n  );\n\n  return (\n    <div className=\"space-y-4\">\n      {/* 标题和计数器 */}\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-medium text-gray-900\">选择标签</h3>\n        <span className=\"text-sm text-gray-500\">\n          已选择 {selectedTags.length}/{maxTags} 个标签\n        </span>\n      </div>\n\n      {/* 已选择的标签 */}\n      {selectedTags.length > 0 && (\n        <div className=\"space-y-2\">\n          <h4 className=\"text-sm font-medium text-gray-700\">已选择的标签：</h4>\n          <div className=\"flex flex-wrap gap-2\">\n            {selectedTags.map((tag) => (\n              <span\n                key={tag}\n                className=\"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800\"\n              >\n                {tag}\n                <button\n                  type=\"button\"\n                  onClick={() => removeTag(tag)}\n                  className=\"ml-2 text-blue-600 hover:text-blue-800\"\n                >\n                  <X className=\"h-3 w-3\" />\n                </button>\n              </span>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* 标签选择器 */}\n      <div className=\"space-y-3\">\n        <div className=\"relative\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            选择标签（最多{maxTags}个）\n          </label>\n\n          {/* 搜索框 */}\n          <div className=\"relative mb-3\">\n            <input\n              type=\"text\"\n              placeholder=\"搜索标签...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              onFocus={() => setIsOpen(true)}\n              className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n            <Search className=\"absolute left-3 top-2.5 h-4 w-4 text-gray-400\" />\n          </div>\n\n          {/* 标签选择下拉框 */}\n          {(isOpen || searchTerm) && (\n            <div className=\"relative\">\n              <div className=\"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto\">\n                {filteredTags.length > 0 ? (\n                  <div className=\"p-2\">\n                    <div className=\"grid grid-cols-1 gap-1\">\n                      {filteredTags.map((tag) => {\n                        const isDisabled = selectedTags.length >= maxTags;\n\n                        return (\n                          <button\n                            key={tag}\n                            type=\"button\"\n                            onClick={() => {\n                              toggleTag(tag);\n                              setSearchTerm('');\n                              setIsOpen(false);\n                            }}\n                            disabled={isDisabled}\n                            className={`\n                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors\n                              ${isDisabled\n                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                                : 'hover:bg-blue-50 text-gray-700'\n                              }\n                            `}\n                          >\n                            <div className=\"flex items-center\">\n                              <Tag className=\"h-3 w-3 mr-2 text-gray-400\" />\n                              {tag}\n                            </div>\n                          </button>\n                        );\n                      })}\n                    </div>\n                    {filteredTags.length > 50 && (\n                      <p className=\"text-xs text-gray-500 mt-2 px-3\">\n                        找到 {filteredTags.length} 个匹配标签\n                      </p>\n                    )}\n                  </div>\n                ) : (\n                  <div className=\"p-4 text-center text-gray-500 text-sm\">\n                    {searchTerm ? '未找到匹配的标签' : '开始输入以搜索标签'}\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* 点击外部关闭下拉框 */}\n      {(isOpen || searchTerm) && (\n        <div\n          className=\"fixed inset-0 z-0\"\n          onClick={() => {\n            setIsOpen(false);\n            setSearchTerm('');\n          }}\n        />\n      )}\n\n      {/* 提示信息 */}\n      {selectedTags.length >= maxTags && (\n        <p className=\"text-sm text-amber-600\">\n          最多只能选择{maxTags}个标签\n        </p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAYe,SAAS,YAAY,EAClC,YAAY,EACZ,YAAY,EACZ,UAAU,2HAAA,CAAA,iBAAc,EACP;;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,YAAY,CAAC;QACjB,IAAI,aAAa,QAAQ,CAAC,MAAM;YAC9B,aAAa,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM;QAC9C,OAAO,IAAI,aAAa,MAAM,GAAG,SAAS;YACxC,aAAa;mBAAI;gBAAc;aAAI;QACrC;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,aAAa,aAAa,MAAM,CAAC,CAAA,IAAK,MAAM;IAC9C;IAEA,yBAAyB;IACzB,MAAM,eAAe,2HAAA,CAAA,iBAAc,CAAC,MAAM,CAAC,CAAA,MACzC,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACjD,CAAC,aAAa,QAAQ,CAAC;IAGzB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,6LAAC;wBAAK,WAAU;;4BAAwB;4BACjC,aAAa,MAAM;4BAAC;4BAAE;4BAAQ;;;;;;;;;;;;;YAKtC,aAAa,MAAM,GAAG,mBACrB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,6LAAC;gCAEC,WAAU;;oCAET;kDACD,6LAAC;wCACC,MAAK;wCACL,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;+BATV;;;;;;;;;;;;;;;;0BAkBf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,WAAU;;gCAA+C;gCACtD;gCAAQ;;;;;;;sCAIlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,SAAS,IAAM,UAAU;oCACzB,WAAU;;;;;;8CAEZ,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;wBAInB,CAAC,UAAU,UAAU,mBACpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,aAAa,MAAM,GAAG,kBACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC;gDACjB,MAAM,aAAa,aAAa,MAAM,IAAI;gDAE1C,qBACE,6LAAC;oDAEC,MAAK;oDACL,SAAS;wDACP,UAAU;wDACV,cAAc;wDACd,UAAU;oDACZ;oDACA,UAAU;oDACV,WAAW,CAAC;;8BAEV,EAAE,aACE,iDACA,iCACH;4BACH,CAAC;8DAED,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DACd;;;;;;;mDAlBE;;;;;4CAsBX;;;;;;wCAED,aAAa,MAAM,GAAG,oBACrB,6LAAC;4CAAE,WAAU;;gDAAkC;gDACzC,aAAa,MAAM;gDAAC;;;;;;;;;;;;yDAK9B,6LAAC;oCAAI,WAAU;8CACZ,aAAa,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUxC,CAAC,UAAU,UAAU,mBACpB,6LAAC;gBACC,WAAU;gBACV,SAAS;oBACP,UAAU;oBACV,cAAc;gBAChB;;;;;;YAKH,aAAa,MAAM,IAAI,yBACtB,6LAAC;gBAAE,WAAU;;oBAAyB;oBAC7B;oBAAQ;;;;;;;;;;;;;AAKzB;GAxJwB;KAAA", "debugId": null}}, {"offset": {"line": 1200, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/submit/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport Layout from '@/components/Layout';\nimport LoadingSpinner from '@/components/LoadingSpinner';\nimport ErrorMessage from '@/components/ErrorMessage';\nimport SuccessMessage from '@/components/SuccessMessage';\nimport LoginModal from '@/components/auth/LoginModal';\nimport { TOOL_PRICING_FORM_OPTIONS } from '@/constants/pricing';\nimport {\n  Upload,\n  Link as LinkIcon,\n  Info\n} from 'lucide-react';\nimport { MAX_TAGS_COUNT } from '@/constants/tags';\nimport TagSelector from '@/components/TagSelector';\n\nconst categories = [\n  { value: 'text-generation', label: '文本生成' },\n  { value: 'image-generation', label: '图像生成' },\n  { value: 'code-generation', label: '代码生成' },\n  { value: 'data-analysis', label: '数据分析' },\n  { value: 'audio-processing', label: '音频处理' },\n  { value: 'video-editing', label: '视频编辑' },\n  { value: 'translation', label: '语言翻译' },\n  { value: 'search-engines', label: '搜索引擎' },\n  { value: 'education', label: '教育学习' },\n  { value: 'marketing', label: '营销工具' },\n  { value: 'productivity', label: '生产力工具' },\n  { value: 'customer-service', label: '客户服务' }\n];\n\n// 使用统一的价格选项配置\nconst pricingOptions = TOOL_PRICING_FORM_OPTIONS;\n\ninterface FormData {\n  name: string;\n  tagline: string;\n  description: string;\n  website: string;\n  logo: string;\n  category: string;\n  tags: string[];\n  pricing: string;\n}\n\nexport default function SubmitPage() {\n  const { data: session } = useSession();\n  const router = useRouter();\n  const [formData, setFormData] = useState<FormData>({\n    name: '',\n    tagline: '',\n    description: '',\n    website: '',\n    logo: '',\n    category: '',\n    tags: [],\n    pricing: ''\n  });\n\n\n\n\n\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');\n  const [errors, setErrors] = useState<Record<string, string>>({});\n  const [submitMessage, setSubmitMessage] = useState('');\n  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);\n  const [logoFile, setLogoFile] = useState<File | null>(null);\n  const [logoPreview, setLogoPreview] = useState<string>('');\n\n  const validateForm = (): boolean => {\n    const newErrors: Record<string, string> = {};\n\n    if (!formData.name.trim()) newErrors.name = '工具名称是必填项';\n    if (!formData.description.trim()) newErrors.description = '工具描述是必填项';\n    if (!formData.website.trim()) newErrors.website = '官方网站是必填项';\n    if (!formData.category) newErrors.category = '请选择一个分类';\n    if (!formData.pricing) newErrors.pricing = '请选择价格模式';\n\n    // URL validation\n    if (formData.website && !formData.website.match(/^https?:\\/\\/.+/)) {\n      newErrors.website = '请输入有效的网站地址（以 http:// 或 https:// 开头）';\n    }\n\n\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // 检查用户是否已登录\n    if (!session) {\n      setIsLoginModalOpen(true);\n      return;\n    }\n\n    if (!validateForm()) return;\n\n    setIsSubmitting(true);\n    setSubmitStatus('idle');\n\n    try {\n      let logoUrl = formData.logo;\n\n      // 如果有选择的logo文件，先上传\n      if (logoFile) {\n        const logoFormData = new FormData();\n        logoFormData.append('logo', logoFile);\n\n        const uploadResponse = await fetch('/api/upload/logo', {\n          method: 'POST',\n          body: logoFormData,\n        });\n\n        const uploadData = await uploadResponse.json();\n        if (uploadData.success) {\n          logoUrl = uploadData.data.url;\n        } else {\n          throw new Error(uploadData.message || 'Logo上传失败');\n        }\n      }\n\n      // 调用新的提交API\n      const response = await fetch('/api/tools/submit', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          name: formData.name,\n          tagline: formData.tagline,\n          description: formData.description,\n          website: formData.website,\n          logo: logoUrl || undefined,\n          category: formData.category,\n          tags: formData.tags,\n          pricing: formData.pricing\n        }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        // 跳转到发布日期选择页面\n        router.push(`/submit/launch-date/${data.data.toolId}`);\n      } else {\n        setSubmitStatus('error');\n        setSubmitMessage(data.message || '提交失败，请重试');\n      }\n    } catch (error) {\n      console.error('Error submitting tool:', error);\n      setSubmitStatus('error');\n      setSubmitMessage('网络错误，请检查连接后重试');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n\n\n  // 处理logo文件选择\n  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const file = e.target.files?.[0];\n    if (file) {\n      setLogoFile(file);\n      // 创建预览URL\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setLogoPreview(e.target?.result as string);\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  return (\n    <Layout>\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            <Upload className=\"inline-block mr-3 h-8 w-8 text-blue-600\" />\n            提交 AI 工具\n          </h1>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            分享您发现或开发的优秀 AI 工具，帮助更多人发现和使用这些工具。我们会在审核通过后发布您的提交。\n          </p>\n        </div>\n\n        {/* Success/Error Messages */}\n        {submitStatus === 'success' && (\n          <SuccessMessage\n            message={submitMessage || '工具提交成功！我们会在 1-3 个工作日内审核您的提交。'}\n            onClose={() => setSubmitStatus('idle')}\n            className=\"mb-6\"\n          />\n        )}\n\n        {submitStatus === 'error' && (\n          <ErrorMessage\n            message={submitMessage || '提交失败，请检查网络连接后重试。'}\n            onClose={() => setSubmitStatus('idle')}\n            className=\"mb-6\"\n          />\n        )}\n\n        {/* Form */}\n        <form onSubmit={handleSubmit} className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-8\">\n          {/* Basic Information */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">基本信息</h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  工具名称 *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.name}\n                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                    errors.name ? 'border-red-300' : 'border-gray-300'\n                  }`}\n                  placeholder=\"例如：ChatGPT\"\n                />\n                {errors.name && <p className=\"text-red-600 text-sm mt-1\">{errors.name}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  工具标语（可选）\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.tagline}\n                  onChange={(e) => setFormData(prev => ({ ...prev, tagline: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  placeholder=\"简短描述工具的核心价值\"\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  官方网站 *\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type=\"url\"\n                    value={formData.website}\n                    onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}\n                    className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                      errors.website ? 'border-red-300' : 'border-gray-300'\n                    }`}\n                    placeholder=\"https://example.com\"\n                  />\n                  <LinkIcon className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n                </div>\n                {errors.website && <p className=\"text-red-600 text-sm mt-1\">{errors.website}</p>}\n              </div>\n            </div>\n\n            <div className=\"mt-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                工具描述 *\n              </label>\n              <textarea\n                value={formData.description}\n                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n                rows={4}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                  errors.description ? 'border-red-300' : 'border-gray-300'\n                }`}\n                placeholder=\"详细描述这个 AI 工具的功能和特点...\"\n              />\n              {errors.description && <p className=\"text-red-600 text-sm mt-1\">{errors.description}</p>}\n            </div>\n\n            <div className=\"mt-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Logo图片（可选）\n              </label>\n              <div className=\"flex items-center gap-4\">\n                <input\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={handleLogoChange}\n                  className=\"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\"\n                />\n                {logoPreview && (\n                  <div className=\"flex-shrink-0\">\n                    <img\n                      src={logoPreview}\n                      alt=\"Logo预览\"\n                      className=\"w-16 h-16 object-cover rounded-lg border border-gray-300\"\n                    />\n                  </div>\n                )}\n              </div>\n              <p className=\"text-sm text-gray-500 mt-1\">\n                支持 JPEG、PNG、GIF、WebP 格式，文件大小不超过 5MB\n              </p>\n            </div>\n          </div>\n\n          {/* Category and Pricing */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">分类和定价</h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  工具分类 *\n                </label>\n                <select\n                  value={formData.category}\n                  onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                    errors.category ? 'border-red-300' : 'border-gray-300'\n                  }`}\n                >\n                  <option value=\"\">请选择分类</option>\n                  {categories.map(category => (\n                    <option key={category.value} value={category.value}>\n                      {category.label}\n                    </option>\n                  ))}\n                </select>\n                {errors.category && <p className=\"text-red-600 text-sm mt-1\">{errors.category}</p>}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  价格模式 *\n                </label>\n                <select\n                  value={formData.pricing}\n                  onChange={(e) => setFormData(prev => ({ ...prev, pricing: e.target.value }))}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${\n                    errors.pricing ? 'border-red-300' : 'border-gray-300'\n                  }`}\n                >\n                  <option value=\"\">请选择价格模式</option>\n                  {pricingOptions.map(option => (\n                    <option key={option.value} value={option.value}>\n                      {option.label}\n                    </option>\n                  ))}\n                </select>\n                {errors.pricing && <p className=\"text-red-600 text-sm mt-1\">{errors.pricing}</p>}\n              </div>\n            </div>\n          </div>\n\n          {/* Tags */}\n          <div className=\"mb-8\">\n            <TagSelector\n              selectedTags={formData.tags}\n              onTagsChange={(tags) => setFormData(prev => ({ ...prev, tags }))}\n              maxTags={MAX_TAGS_COUNT}\n            />\n          </div>\n\n\n\n          {/* User Info Display */}\n          {session && (\n            <div className=\"mb-8 bg-green-50 border border-green-200 rounded-lg p-4\">\n              <h3 className=\"text-sm font-medium text-green-800 mb-2\">提交者信息</h3>\n              <p className=\"text-sm text-green-700\">\n                提交者：{session.user?.name || session.user?.email}\n              </p>\n              <p className=\"text-sm text-green-700\">\n                邮箱：{session.user?.email}\n              </p>\n            </div>\n          )}\n\n          {/* Guidelines */}\n          <div className=\"mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6\">\n            <div className=\"flex items-start\">\n              <Info className=\"h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0\" />\n              <div>\n                <h3 className=\"text-sm font-medium text-blue-900 mb-2\">提交指南</h3>\n                <ul className=\"text-sm text-blue-800 space-y-1\">\n                  <li>• 请确保提交的是真实存在且可正常访问的 AI 工具</li>\n                  <li>• 工具描述应该准确、客观，避免过度营销</li>\n                  <li>• 我们会在 1-3 个工作日内审核您的提交</li>\n                  <li>• 审核通过后，工具将出现在我们的目录中</li>\n                  <li>• 如有问题，我们会通过邮箱联系您</li>\n                </ul>\n              </div>\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex justify-end\">\n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className={`px-8 py-3 rounded-lg font-medium transition-colors ${\n                isSubmitting\n                  ? 'bg-gray-400 text-gray-700 cursor-not-allowed'\n                  : 'bg-blue-600 text-white hover:bg-blue-700'\n              }`}\n            >\n              {isSubmitting ? (\n                <div className=\"flex items-center\">\n                  <LoadingSpinner size=\"sm\" className=\"mr-2\" />\n                  提交中...\n                </div>\n              ) : (\n                '提交工具'\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n\n      {/* Login Modal */}\n      <LoginModal\n        isOpen={isLoginModalOpen}\n        onClose={() => setIsLoginModalOpen(false)}\n      />\n    </Layout>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAKA;AACA;;;AAjBA;;;;;;;;;;;;;AAmBA,MAAM,aAAa;IACjB;QAAE,OAAO;QAAmB,OAAO;IAAO;IAC1C;QAAE,OAAO;QAAoB,OAAO;IAAO;IAC3C;QAAE,OAAO;QAAmB,OAAO;IAAO;IAC1C;QAAE,OAAO;QAAiB,OAAO;IAAO;IACxC;QAAE,OAAO;QAAoB,OAAO;IAAO;IAC3C;QAAE,OAAO;QAAiB,OAAO;IAAO;IACxC;QAAE,OAAO;QAAe,OAAO;IAAO;IACtC;QAAE,OAAO;QAAkB,OAAO;IAAO;IACzC;QAAE,OAAO;QAAa,OAAO;IAAO;IACpC;QAAE,OAAO;QAAa,OAAO;IAAO;IACpC;QAAE,OAAO;QAAgB,OAAO;IAAQ;IACxC;QAAE,OAAO;QAAoB,OAAO;IAAO;CAC5C;AAED,cAAc;AACd,MAAM,iBAAiB,8HAAA,CAAA,4BAAyB;AAajC,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,MAAM;QACN,SAAS;QACT,aAAa;QACb,SAAS;QACT,MAAM;QACN,UAAU;QACV,MAAM,EAAE;QACR,SAAS;IACX;IAMA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IAC/E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEvD,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG;QAC5C,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI,UAAU,WAAW,GAAG;QAC1D,IAAI,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI,UAAU,OAAO,GAAG;QAClD,IAAI,CAAC,SAAS,QAAQ,EAAE,UAAU,QAAQ,GAAG;QAC7C,IAAI,CAAC,SAAS,OAAO,EAAE,UAAU,OAAO,GAAG;QAE3C,iBAAiB;QACjB,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,KAAK,CAAC,mBAAmB;YACjE,UAAU,OAAO,GAAG;QACtB;QAIA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,YAAY;QACZ,IAAI,CAAC,SAAS;YACZ,oBAAoB;YACpB;QACF;QAEA,IAAI,CAAC,gBAAgB;QAErB,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,IAAI,UAAU,SAAS,IAAI;YAE3B,mBAAmB;YACnB,IAAI,UAAU;gBACZ,MAAM,eAAe,IAAI;gBACzB,aAAa,MAAM,CAAC,QAAQ;gBAE5B,MAAM,iBAAiB,MAAM,MAAM,oBAAoB;oBACrD,QAAQ;oBACR,MAAM;gBACR;gBAEA,MAAM,aAAa,MAAM,eAAe,IAAI;gBAC5C,IAAI,WAAW,OAAO,EAAE;oBACtB,UAAU,WAAW,IAAI,CAAC,GAAG;gBAC/B,OAAO;oBACL,MAAM,IAAI,MAAM,WAAW,OAAO,IAAI;gBACxC;YACF;YAEA,YAAY;YACZ,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM,SAAS,IAAI;oBACnB,SAAS,SAAS,OAAO;oBACzB,aAAa,SAAS,WAAW;oBACjC,SAAS,SAAS,OAAO;oBACzB,MAAM,WAAW;oBACjB,UAAU,SAAS,QAAQ;oBAC3B,MAAM,SAAS,IAAI;oBACnB,SAAS,SAAS,OAAO;gBAC3B;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,cAAc;gBACd,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,KAAK,IAAI,CAAC,MAAM,EAAE;YACvD,OAAO;gBACL,gBAAgB;gBAChB,iBAAiB,KAAK,OAAO,IAAI;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,gBAAgB;YAChB,iBAAiB;QACnB,SAAU;YACR,gBAAgB;QAClB;IACF;IAIA,aAAa;IACb,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QAChC,IAAI,MAAM;YACR,YAAY;YACZ,UAAU;YACV,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,eAAe,EAAE,MAAM,EAAE;YAC3B;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,qBACE,6LAAC,+HAAA,CAAA,UAAM;;0BACL,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAA4C;;;;;;;0CAGhE,6LAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;oBAMxD,iBAAiB,2BAChB,6LAAC,uIAAA,CAAA,UAAc;wBACb,SAAS,iBAAiB;wBAC1B,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;;;;;;oBAIb,iBAAiB,yBAChB,6LAAC,qIAAA,CAAA,UAAY;wBACX,SAAS,iBAAiB;wBAC1B,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;;;;;;kCAKd,6LAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAEzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,MAAK;wDACL,OAAO,SAAS,IAAI;wDACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDACvE,WAAW,CAAC,6FAA6F,EACvG,OAAO,IAAI,GAAG,mBAAmB,mBACjC;wDACF,aAAY;;;;;;oDAEb,OAAO,IAAI,kBAAI,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,IAAI;;;;;;;;;;;;0DAGvE,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC1E,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAKlB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC1E,WAAW,CAAC,mGAAmG,EAC7G,OAAO,OAAO,GAAG,mBAAmB,mBACpC;4DACF,aAAY;;;;;;sEAEd,6LAAC,qMAAA,CAAA,OAAQ;4DAAC,WAAU;;;;;;;;;;;;gDAErB,OAAO,OAAO,kBAAI,6LAAC;oDAAE,WAAU;8DAA6B,OAAO,OAAO;;;;;;;;;;;;;;;;;kDAI/E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,OAAO,SAAS,WAAW;gDAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC9E,MAAM;gDACN,WAAW,CAAC,6FAA6F,EACvG,OAAO,WAAW,GAAG,mBAAmB,mBACxC;gDACF,aAAY;;;;;;4CAEb,OAAO,WAAW,kBAAI,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,WAAW;;;;;;;;;;;;kDAGrF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,QAAO;wDACP,UAAU;wDACV,WAAU;;;;;;oDAEX,6BACC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,KAAK;4DACL,KAAI;4DACJ,WAAU;;;;;;;;;;;;;;;;;0DAKlB,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;0CAO9C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAEzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,OAAO,SAAS,QAAQ;wDACxB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC3E,WAAW,CAAC,6FAA6F,EACvG,OAAO,QAAQ,GAAG,mBAAmB,mBACrC;;0EAEF,6LAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;oEAA4B,OAAO,SAAS,KAAK;8EAC/C,SAAS,KAAK;mEADJ,SAAS,KAAK;;;;;;;;;;;oDAK9B,OAAO,QAAQ,kBAAI,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,QAAQ;;;;;;;;;;;;0DAG/E,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,6LAAC;wDACC,OAAO,SAAS,OAAO;wDACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC1E,WAAW,CAAC,6FAA6F,EACvG,OAAO,OAAO,GAAG,mBAAmB,mBACpC;;0EAEF,6LAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,eAAe,GAAG,CAAC,CAAA,uBAClB,6LAAC;oEAA0B,OAAO,OAAO,KAAK;8EAC3C,OAAO,KAAK;mEADF,OAAO,KAAK;;;;;;;;;;;oDAK5B,OAAO,OAAO,kBAAI,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;;;0CAMjF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,oIAAA,CAAA,UAAW;oCACV,cAAc,SAAS,IAAI;oCAC3B,cAAc,CAAC,OAAS,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE;4CAAK,CAAC;oCAC9D,SAAS,2HAAA,CAAA,iBAAc;;;;;;;;;;;4BAO1B,yBACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA0C;;;;;;kDACxD,6LAAC;wCAAE,WAAU;;4CAAyB;4CAC/B,QAAQ,IAAI,EAAE,QAAQ,QAAQ,IAAI,EAAE;;;;;;;kDAE3C,6LAAC;wCAAE,WAAU;;4CAAyB;4CAChC,QAAQ,IAAI,EAAE;;;;;;;;;;;;;0CAMxB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;sEACJ,6LAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOZ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAW,CAAC,mDAAmD,EAC7D,eACI,iDACA,4CACJ;8CAED,6BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uIAAA,CAAA,UAAc;gDAAC,MAAK;gDAAK,WAAU;;;;;;4CAAS;;;;;;+CAI/C;;;;;;;;;;;;;;;;;;;;;;;0BAQV,6LAAC,2IAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,SAAS,IAAM,oBAAoB;;;;;;;;;;;;AAI3C;GAlYwB;;QACI,iJAAA,CAAA,aAAU;QACrB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}