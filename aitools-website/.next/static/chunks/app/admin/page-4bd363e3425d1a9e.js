(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698],{450:(e,s,t)=>{Promise.resolve().then(t.bind(t,7220))},2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2731:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(5155);function r(e){let{size:s="md",className:t=""}=e;return(0,a.jsx)("div",{className:"flex justify-center items-center ".concat(t),children:(0,a.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[s]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},4416:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5339:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5525:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5731:(e,s,t)=>{"use strict";t.d(s,{u:()=>l});let a=t(9509).env.NEXT_PUBLIC_API_BASE_URL||"http://localhost:3001/api";class r{async request(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let t="".concat(this.baseURL).concat(e),a={headers:{"Content-Type":"application/json",...s.headers},...s},r=await fetch(t,a),l=await r.json();if(!r.ok)throw Error(l.error||"HTTP error! status: ".concat(r.status));return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,a]=e;void 0!==a&&s.append(t,a.toString())});let t=s.toString();return this.request("/tools".concat(t?"?".concat(t):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,s){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(s)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getLikedTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,a]=e;void 0!==a&&s.append(t,a.toString())});let t=s.toString();return this.request("/user/liked-tools".concat(t?"?".concat(t):""))}async getAdminTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[t,a]=e;void 0!==a&&s.append(t,a.toString())});let t=s.toString();return this.request("/admin/tools".concat(t?"?".concat(t):""))}async approveTool(e,s){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(s)})}async rejectTool(e,s){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(s)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}constructor(e=a){this.baseURL=e}}let l=new r},5734:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var a=t(5155),r=t(646),l=t(4416);function n(e){let{message:s,onClose:t,className:n=""}=e;return(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 ".concat(n),children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(r.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("p",{className:"text-green-800 text-sm",children:s})}),t&&(0,a.jsx)("button",{onClick:t,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,a.jsx)(l.A,{className:"w-4 h-4"})})]})})}},6932:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7220:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var a=t(5155),r=t(2115),l=t(4478),n=t(2731),c=t(9783),i=t(5734),d=t(5731),o=t(4186),x=t(646),m=t(4861),u=t(5525),h=t(7924),g=t(6932),p=t(1243),y=t(1007),j=t(9074),b=t(3786),f=t(2657);let N={"text-generation":"文本生成","image-generation":"图像生成","code-generation":"代码生成","data-analysis":"数据分析","audio-processing":"音频处理","video-editing":"视频编辑",translation:"语言翻译","search-engines":"搜索引擎",education:"教育学习",marketing:"营销工具",productivity:"生产力工具","customer-service":"客户服务"},v={free:"免费",freemium:"免费增值",paid:"付费"};function w(){let[e,s]=(0,r.useState)([]),[t,w]=(0,r.useState)(!0),[A,k]=(0,r.useState)(""),[S,C]=(0,r.useState)(""),[T,L]=(0,r.useState)(""),[q,E]=(0,r.useState)("all"),[O,_]=(0,r.useState)(null),[P,R]=(0,r.useState)(!1),[U,z]=(0,r.useState)(""),[D,B]=(0,r.useState)(!1);(0,r.useEffect)(()=>{I()},[q]);let I=async()=>{try{w(!0),k("");let e=await d.u.getAdminTools({status:"all"===q?void 0:q,limit:50});e.success&&e.data?s(e.data.tools):k(e.error||"获取工具列表失败")}catch(e){k("网络错误，请重试")}finally{w(!1)}},J=e.filter(e=>{let s=e.name.toLowerCase().includes(T.toLowerCase())||e.description.toLowerCase().includes(T.toLowerCase()),t="all"===q||e.status===q;return s&&t}),M=async e=>{try{B(!0),k("");let s=await d.u.approveTool(e,{reviewedBy:"admin",reviewNotes:"审核通过",publishedAt:new Date().toISOString()});s.success?(C("工具审核通过！"),await I()):k(s.error||"审核操作失败")}catch(e){k("网络错误，请重试")}finally{B(!1)}},H=async(e,s)=>{try{B(!0),k("");let t=await d.u.rejectTool(e,{reviewedBy:"admin",rejectReason:s});t.success?(C("工具已拒绝！"),await I(),R(!1),z(""),_(null)):k(t.error||"拒绝操作失败")}catch(e){k("网络错误，请重试")}finally{B(!1)}},F=e=>new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),V=e=>{switch(e){case"pending":return(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[(0,a.jsx)(o.A,{className:"w-3 h-3 mr-1"}),"待审核"]});case"approved":return(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[(0,a.jsx)(x.A,{className:"w-3 h-3 mr-1"}),"已批准"]});case"rejected":return(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:[(0,a.jsx)(m.A,{className:"w-3 h-3 mr-1"}),"已拒绝"]});default:return null}};return t?(0,a.jsx)(l.A,{children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)(n.A,{size:"lg",className:"py-20"})})}):(0,a.jsx)(l.A,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 flex items-center",children:[(0,a.jsx)(u.A,{className:"mr-3 h-8 w-8 text-blue-600"}),"管理员审核中心"]}),(0,a.jsx)("p",{className:"mt-2 text-lg text-gray-600",children:"审核和管理用户提交的 AI 工具"})]}),(0,a.jsxs)("div",{className:"flex space-x-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:e.filter(e=>"pending"===e.status).length}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"待审核"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:e.filter(e=>"approved"===e.status).length}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"已批准"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:e.filter(e=>"rejected"===e.status).length}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"已拒绝"})]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(h.A,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"搜索工具名称、描述或提交者...",value:T,onChange:e=>L(e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,a.jsx)("div",{className:"sm:w-48",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-2.5 h-5 w-5 text-gray-400"}),(0,a.jsxs)("select",{value:q,onChange:e=>E(e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none",children:[(0,a.jsx)("option",{value:"all",children:"所有状态"}),(0,a.jsx)("option",{value:"pending",children:"待审核"}),(0,a.jsx)("option",{value:"approved",children:"已批准"}),(0,a.jsx)("option",{value:"rejected",children:"已拒绝"})]})]})})]})}),S&&(0,a.jsx)(i.A,{message:S,onClose:()=>C(""),className:"mb-6"}),A&&(0,a.jsx)(c.A,{message:A,onClose:()=>k(""),className:"mb-6"}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:0===J.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(p.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"没有找到工具"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:T||"all"!==q?"尝试调整搜索条件或筛选器":"暂无待审核的工具"})]}):(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:J.map(e=>(0,a.jsx)("div",{className:"p-6 hover:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("img",{src:e.logo,alt:e.name,className:"w-12 h-12 rounded-lg object-cover border border-gray-200"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 truncate",children:e.name}),V(e.status),(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:N[e.category]}),(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:v[e.pricing]})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3 line-clamp-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(y.A,{className:"w-4 h-4 mr-1"}),e.submittedBy]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"w-4 h-4 mr-1"}),F(e.submittedAt)]}),e.publishedAt&&(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"w-4 h-4 mr-1"}),"已发布: ",new Date(e.publishedAt).toLocaleDateString("zh-CN")]})]}),e.tags.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-1 mt-3",children:e.tags.map((e,s)=>(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-700",children:e},s))})]})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,a.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"p-2 text-gray-400 hover:text-gray-600 transition-colors",title:"访问网站",children:(0,a.jsx)(b.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{className:"p-2 text-gray-400 hover:text-gray-600 transition-colors",title:"查看详情",children:(0,a.jsx)(f.A,{className:"w-4 h-4"})}),"pending"===e.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>M(e._id),disabled:D,className:"px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:D?"处理中...":"批准"}),(0,a.jsx)("button",{onClick:()=>{_(e._id),R(!0)},disabled:D,className:"px-3 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:"拒绝"})]})]})]})},e.id))})}),P&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"拒绝工具"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"请说明拒绝的原因，这将帮助提交者改进他们的提交。"}),(0,a.jsx)("textarea",{value:U,onChange:e=>z(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入拒绝原因..."}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,a.jsx)("button",{onClick:()=>{R(!1),z(""),_(null)},className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors",children:"取消"}),(0,a.jsx)("button",{onClick:()=>O&&H(O,U),disabled:!U.trim()||D,className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:D?"处理中...":"确认拒绝"})]})]})})]})})}},7924:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9783:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});var a=t(5155),r=t(5339),l=t(4416);function n(e){let{message:s,onClose:t,className:n=""}=e;return(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(n),children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(r.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("p",{className:"text-red-800 text-sm",children:s})}),t&&(0,a.jsx)("button",{onClick:t,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,a.jsx)(l.A,{className:"w-4 h-4"})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,9448,8441,1684,7358],()=>s(450)),_N_E=e.O()}]);