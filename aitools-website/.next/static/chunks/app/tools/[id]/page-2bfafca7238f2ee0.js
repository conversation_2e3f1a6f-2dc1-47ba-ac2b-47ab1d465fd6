(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8664],{365:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});var a=t(2115);function r(){return(0,a.useEffect)(()=>{let e={},s=(e,s)=>{window.gtag&&window.gtag("event","web_vitals",{event_category:"Performance",event_label:e,value:Math.round(s),non_interaction:!0})};if("undefined"!=typeof PerformanceObserver){let t;new PerformanceObserver(t=>{let a=t.getEntries().find(e=>"first-contentful-paint"===e.name);a&&(e.fcp=a.startTime,s("FCP",a.startTime))}).observe({entryTypes:["paint"]}),new PerformanceObserver(t=>{let a=t.getEntries(),r=a[a.length-1];e.lcp=r.startTime,s("LCP",r.startTime)}).observe({entryTypes:["largest-contentful-paint"]}),new PerformanceObserver(t=>{t.getEntries().forEach(t=>{e.fid=t.processingStart-t.startTime,s("FID",t.processingStart-t.startTime)})}).observe({entryTypes:["first-input"]}),t=0,new PerformanceObserver(a=>{a.getEntries().forEach(e=>{e.hadRecentInput||(t+=e.value)}),e.cls=t,s("CLS",t)}).observe({entryTypes:["layout-shift"]})}let t=performance.getEntriesByType("navigation")[0];if(t){let a=t.responseStart-t.requestStart;e.ttfb=a,s("TTFB",a)}let a=()=>{Object.keys(e).length>0&&console.log("Final Performance Metrics:",e)};return window.addEventListener("beforeunload",a),()=>{window.removeEventListener("beforeunload",a)}},[]),null}},2984:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6874,23)),Promise.resolve().then(t.bind(t,365)),Promise.resolve().then(t.bind(t,6401))},3467:(e,s,t)=>{"use strict";t.d(s,{$g:()=>d,Ef:()=>c,Y$:()=>i,kX:()=>a,mV:()=>o,tF:()=>m,v4:()=>n,vS:()=>r});let a={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},r=[{id:"free",title:"免费发布",description:a.FREE_LAUNCH.description,price:a.FREE_LAUNCH.displayPrice,features:a.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:a.PRIORITY_LAUNCH.description,price:a.PRIORITY_LAUNCH.displayPrice,features:a.PRIORITY_LAUNCH.features,recommended:!0}],l={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},n=[{value:"",label:"所有价格"},{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],i=[{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],c=e=>{switch(e){case l.FREE.value:return l.FREE.color;case l.FREEMIUM.value:return l.FREEMIUM.color;case l.PAID.value:return l.PAID.color;default:return"bg-gray-100 text-gray-800"}},o=e=>{switch(e){case l.FREE.value:return l.FREE.label;case l.FREEMIUM.value:return l.FREEMIUM.label;case l.PAID.value:return l.PAID.label;default:return e}},d=e=>0===e?"免费":"\xa5".concat(e),m=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:s.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},4601:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(5155),r=t(2115),l=t(2108),n=t(9911);function i(e){let{toolId:s,initialLikes:t=0,initialLiked:i=!1,onLoginRequired:c,onUnlike:o,isInLikedPage:d=!1}=e,{data:m}=(0,l.useSession)(),[x,u]=(0,r.useState)(i),[h,g]=(0,r.useState)(t),[p,b]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=async()=>{try{let e=await fetch("/api/tools/".concat(s,"/like"));if(e.ok){let s=await e.json();s.success&&(u(s.data.liked),g(s.data.likes))}}catch(e){console.error("Failed to fetch like status:",e)}};m&&e()},[s,m]);let f=async()=>{if(!m){null==c||c();return}if(!p){b(!0);try{let e=await fetch("/api/tools/".concat(s,"/like"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(d?{forceUnlike:!0}:{})});if(e.ok){let t=await e.json();if(t.success){let e=t.data.liked;u(e),g(t.data.likes),!e&&o&&o(s)}}else{let s=await e.json();console.error("Like failed:",s.message)}}catch(e){console.error("Like request failed:",e)}finally{b(!1)}}};return(0,a.jsxs)("button",{onClick:f,disabled:p,className:"\n        flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200\n        ".concat(x?"bg-red-50 text-red-600 hover:bg-red-100":"bg-gray-50 text-gray-600 hover:bg-gray-100","\n        ").concat(p?"opacity-50 cursor-not-allowed":"hover:scale-105","\n        border border-gray-200 hover:border-gray-300\n      "),children:[x?(0,a.jsx)(n.Mbv,{className:"w-4 h-4 text-red-500"}):(0,a.jsx)(n.sOK,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:h>0?h:""})]})}},6063:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var a=t(5155),r=t(2115),l=t(2108),n=t(9911);function i(e){let{isOpen:s,onClose:t}=e,[i,c]=(0,r.useState)("method"),[o,d]=(0,r.useState)(""),[m,x]=(0,r.useState)(""),[u,h]=(0,r.useState)(!1),[g,p]=(0,r.useState)(""),b=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",t=document.createElement("div");t.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===s?"bg-green-500":"bg-red-500"),t.textContent=e,document.body.appendChild(t),setTimeout(()=>document.body.removeChild(t),3e3)},f=()=>{c("method"),d(""),x(""),p(""),t()},y=async e=>{try{h(!0),await (0,l.signIn)(e,{callbackUrl:"/"})}catch(e){b("登录失败，请稍后重试","error")}finally{h(!1)}},j=async()=>{if(!o)return void p("请输入邮箱地址");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o))return void p("请输入有效的邮箱地址");p(""),h(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:o})}),s=await e.json();s.success?(x(s.token),c("code"),b("验证码已发送，请查看您的邮箱")):b(s.error||"发送失败，请稍后重试","error")}catch(e){b("网络错误，请检查网络连接","error")}finally{h(!1)}},v=async e=>{if(6===e.length){h(!0);try{let s=await (0,l.signIn)("email-code",{email:o,code:e,token:m,redirect:!1});(null==s?void 0:s.ok)?(b("登录成功，欢迎回来！"),f()):b((null==s?void 0:s.error)||"验证码错误","error")}catch(e){b("网络错误，请检查网络连接","error")}finally{h(!1)}}},N=(e,s)=>{if(s.length>1)return;let t=document.querySelectorAll(".code-input");if(t[e].value=s,s&&e<5){var a;null==(a=t[e+1])||a.focus()}let r=Array.from(t).map(e=>e.value).join("");6===r.length&&v(r)};return s?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:f}),(0,a.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===i&&"登录 AI Tools Directory","email"===i&&"邮箱登录","code"===i&&"输入验证码"]}),(0,a.jsx)("button",{onClick:f,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(n.QCr,{})})]}),(0,a.jsxs)("div",{className:"p-6",children:["method"===i&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:"选择登录方式"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>y("google"),disabled:u,children:[(0,a.jsx)(n.DSS,{}),"使用 Google 登录"]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>y("github"),disabled:u,children:[(0,a.jsx)(n.hL4,{}),"使用 GitHub 登录"]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>c("email"),children:[(0,a.jsx)(n.maD,{}),"使用邮箱登录"]})]}),"email"===i&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:"输入您的邮箱地址，我们将发送验证码"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),(0,a.jsx)("input",{type:"email",value:o,onChange:e=>d(e.target.value),placeholder:"请输入邮箱地址",onKeyPress:e=>"Enter"===e.key&&j(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),g&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:g})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:j,disabled:u,children:u?"发送中...":"发送验证码"}),(0,a.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("method"),children:"返回"})]})]}),"code"===i&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("p",{className:"text-gray-600 text-center",children:["请输入发送到 ",o," 的6位验证码"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),(0,a.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,a.jsx)("input",{type:"text",maxLength:1,onChange:s=>N(e,s.target.value),disabled:u,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("email"),children:"重新发送验证码"}),(0,a.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>c("method"),children:"返回"})]})]})]})]})]}):null}},6401:(e,s,t)=>{"use strict";t.d(s,{default:()=>y});var a=t(5155),r=t(2115),l=t(6874),n=t.n(l),i=t(4601),c=t(2108),o=t(9911);function d(e){let{toolId:s,onLoginRequired:t}=e,{data:l}=(0,c.useSession)(),[n,i]=(0,r.useState)([]),[d,m]=(0,r.useState)(""),[x,u]=(0,r.useState)(null),[h,g]=(0,r.useState)(""),[p,b]=(0,r.useState)(!1),[f,y]=(0,r.useState)(!1),j=async()=>{b(!0);try{let e=await fetch("/api/tools/".concat(s,"/comments"));if(e.ok){let s=await e.json();s.success&&i(s.data.comments)}}catch(e){console.error("Failed to fetch comments:",e)}finally{b(!1)}};(0,r.useEffect)(()=>{j()},[s]);let v=async()=>{if(!l){null==t||t();return}if(d.trim()){y(!0);try{let e=await fetch("/api/tools/".concat(s,"/comments"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:d.trim()})});if(e.ok)(await e.json()).success&&(m(""),j());else{let s=await e.json();console.error("Comment submission failed:",s.message)}}catch(e){console.error("Comment submission error:",e)}finally{y(!1)}}},N=async e=>{if(!l){null==t||t();return}if(h.trim()){y(!0);try{let t=await fetch("/api/tools/".concat(s,"/comments"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:h.trim(),parentId:e})});if(t.ok)(await t.json()).success&&(g(""),u(null),j());else{let e=await t.json();console.error("Reply submission failed:",e.message)}}catch(e){console.error("Reply submission error:",e)}finally{y(!1)}}},w=e=>{let s=new Date(e),t=Math.floor((new Date().getTime()-s.getTime())/36e5);return t<1?"刚刚":t<24?"".concat(t,"小时前"):t<168?"".concat(Math.floor(t/24),"天前"):s.toLocaleDateString("zh-CN")},E=e=>{let{comment:s,isReply:t=!1}=e;return(0,a.jsx)("div",{className:"".concat(t?"ml-8 border-l-2 border-gray-100 pl-4":""),children:(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:s.userId.image?(0,a.jsx)("img",{src:s.userId.image,alt:s.userId.name,className:"w-8 h-8 rounded-full"}):(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,a.jsx)(o.x$1,{className:"w-4 h-4 text-gray-600"})})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("span",{className:"font-medium text-gray-900",children:s.userId.name}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:w(s.createdAt)})]}),(0,a.jsx)("p",{className:"text-gray-700 mb-2",children:s.content}),(0,a.jsx)("div",{className:"flex items-center gap-4",children:!t&&(0,a.jsxs)("button",{onClick:()=>u(x===s._id?null:s._id),className:"text-sm text-gray-500 hover:text-gray-700 flex items-center gap-1",children:[(0,a.jsx)(o.w1Z,{className:"w-3 h-3"}),"回复"]})}),x===s._id&&(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsx)("textarea",{value:h,onChange:e=>g(e.target.value),placeholder:"写下你的回复...",className:"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:3,maxLength:1e3}),(0,a.jsxs)("div",{className:"flex justify-end gap-2 mt-2",children:[(0,a.jsx)("button",{onClick:()=>{u(null),g("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800",children:"取消"}),(0,a.jsx)("button",{onClick:()=>N(s._id),disabled:f||!h.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:f?"发送中...":"发送"})]})]}),s.replies&&s.replies.length>0&&(0,a.jsx)("div",{className:"mt-4 space-y-4",children:s.replies.map(e=>(0,a.jsx)(E,{comment:e,isReply:!0},e._id))})]})]})})};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["评论 (",n.length,")"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("textarea",{value:d,onChange:e=>m(e.target.value),placeholder:l?"写下你的评论...":"请先登录后评论",className:"w-full p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:4,maxLength:1e3,disabled:!l}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[d.length,"/1000"]}),(0,a.jsx)("button",{onClick:v,disabled:f||!d.trim()||!l,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:f?"发送中...":"发表评论"})]})]}),p?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-500 mt-2",children:"加载评论中..."})]}):0===n.length?(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:"暂无评论，来发表第一条评论吧！"})}):(0,a.jsx)("div",{className:"space-y-6",children:n.map(e=>(0,a.jsx)(E,{comment:e},e._id))})]})}var m=t(6063),x=t(3467),u=t(5868),h=t(2657),g=t(1976),p=t(6516),b=t(3332),f=t(3786);function y(e){let{initialTool:s,toolId:t,relatedTools:l}=e,[c,o]=(0,r.useState)(s),[y,j]=(0,r.useState)(!1);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)("article",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,a.jsxs)("header",{className:"flex items-start justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[c.logo?(0,a.jsx)("img",{src:c.logo,alt:"".concat(c.name," logo"),className:"w-16 h-16 rounded-lg object-cover"}):(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-2xl",children:c.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:c.name}),c.tagline&&(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-3",children:c.tagline}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat((0,x.Ef)(c.pricing)),children:[(0,a.jsx)(u.A,{className:"mr-1 h-4 w-4"}),(0,x.mV)(c.pricing)]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[c.views||0," 浏览"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(g.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[c.likes||0," 喜欢"]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(i.A,{toolId:c._id,initialLikes:c.likes,onLoginRequired:()=>j(!0)}),(0,a.jsx)("button",{className:"p-2 text-gray-400 hover:text-blue-500 transition-colors",children:(0,a.jsx)(p.A,{className:"h-5 w-5"})})]})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("p",{className:"text-gray-600 text-lg leading-relaxed",children:c.description})}),c.tags&&c.tags.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:c.tags.map((e,s)=>(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 cursor-pointer",children:[(0,a.jsx)(b.A,{className:"mr-1 h-3 w-3"}),e]},s))}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("a",{href:c.website,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(f.A,{className:"mr-2 h-5 w-5"}),"访问 ",c.name]}),(0,a.jsx)(i.A,{toolId:c._id,initialLikes:c.likes,onLoginRequired:()=>j(!0)})]})]})}),(0,a.jsxs)("aside",{className:"lg:col-span-1",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"工具信息"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"分类"}),(0,a.jsx)("span",{className:"text-gray-900 font-medium",children:c.category})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"价格模式"}),(0,a.jsx)("span",{className:"px-2 py-1 rounded text-sm font-medium ".concat((0,x.Ef)(c.pricing)),children:(0,x.mV)(c.pricing)})]}),c.publishedAt&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"发布日期"}),(0,a.jsx)("span",{className:"text-gray-900 font-medium",children:new Date(c.publishedAt).toLocaleDateString("zh-CN")})]})]})]}),l.length>0&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"相关工具"}),(0,a.jsx)("div",{className:"space-y-4",children:l.map(e=>(0,a.jsx)("div",{children:(0,a.jsx)(n(),{href:"/tools/".concat(e._id),className:"block p-3 rounded-lg hover:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[e.logo?(0,a.jsx)("img",{src:e.logo,alt:e.name,className:"w-10 h-10 rounded object-cover"}):(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-sm",children:e.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500 mt-1",children:[(0,a.jsx)("span",{className:"px-2 py-1 rounded ".concat((0,x.Ef)(e.pricing)),children:(0,x.mV)(e.pricing)}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("span",{children:[e.views||0," 浏览"]}),(0,a.jsxs)("span",{children:[e.likes||0," 喜欢"]})]})]})]})]})})},e._id))})]})]})]}),(0,a.jsx)("div",{className:"mt-12",children:(0,a.jsx)(d,{toolId:c._id,onLoginRequired:()=>j(!0)})}),(0,a.jsx)(m.A,{isOpen:y,onClose:()=>j(!1)})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,6874,2108,9167,8441,1684,7358],()=>s(2984)),_N_E=e.O()}]);